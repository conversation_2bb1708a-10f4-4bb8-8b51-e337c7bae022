import request from '@/utils/request'

/**
 * 分页查询IP白名单列表
 * @param {Object} params 查询参数
 * @param {number} params.current 页码
 * @param {number} params.size 每页大小
 * @param {number} params.whitelistType 白名单类型
 * @param {string} params.ipAddress IP地址模糊查询
 * @param {string} params.username 用户名模糊查询
 * @param {number} params.enableStatus 启用状态
 * @returns {Promise} 分页查询结果
 */
export function getIpWhitelistPage(params) {
  return request({
    url: '/system/ip-whitelist/page',
    method: 'get',
    params
  })
}

/**
 * 添加IP白名单
 * @param {Object} data 白名单数据
 * @param {number} data.whitelistType 白名单类型
 * @param {string} data.ipAddress IP地址
 * @param {number} data.enableStatus 启用状态
 * @param {string} data.remark 备注
 * @returns {Promise} 添加结果
 */
export function addIpWhitelist(data) {
  return request({
    url: '/system/ip-whitelist',
    method: 'post',
    data
  })
}

/**
 * 更新IP白名单
 * @param {Object} data 白名单数据
 * @param {string} data.id 主键ID
 * @param {number} data.whitelistType 白名单类型
 * @param {string} data.ipAddress IP地址
 * @param {number} data.enableStatus 启用状态
 * @param {string} data.remark 备注
 * @returns {Promise} 更新结果
 */
export function updateIpWhitelist(data) {
  return request({
    url: '/system/ip-whitelist',
    method: 'put',
    data
  })
}

/**
 * 删除IP白名单
 * @param {string} id 白名单ID
 * @returns {Promise} 删除结果
 */
export function deleteIpWhitelist(id) {
  return request({
    url: `/system/ip-whitelist/${id}`,
    method: 'delete'
  })
}

/**
 * 批量删除IP白名单
 * @param {string[]} ids 白名单ID列表
 * @returns {Promise} 批量删除结果
 */
export function batchDeleteIpWhitelist(ids) {
  return request({
    url: '/system/ip-whitelist/batch',
    method: 'delete',
    data: ids
  })
}

/**
 * 批量更新IP白名单状态
 * @param {string[]} ids 白名单ID列表
 * @param {number} enableStatus 启用状态
 * @returns {Promise} 批量更新结果
 */
export function batchUpdateIpWhitelistStatus(ids, enableStatus) {
  return request({
    url: '/system/ip-whitelist/batch/status',
    method: 'put',
    params: { enableStatus },
    data: ids
  })
}

/**
 * 重置用户白名单
 * @param {string} userId 用户ID（可选）
 * @returns {Promise} 重置结果
 */
export function resetUserWhitelist(userId) {
  return request({
    url: '/system/ip-whitelist/reset',
    method: 'delete',
    params: userId ? { userId } : {}
  })
}

// 白名单类型枚举
export const WhitelistTypeEnum = {
  SYSTEM: 1,
  API: 2
}

// 启用状态枚举
export const EnableStatusEnum = {
  DISABLED: 0,
  ENABLED: 1
}

// 白名单类型选项
export const whitelistTypeOptions = [
  { label: '系统', value: WhitelistTypeEnum.SYSTEM },
  { label: '接口', value: WhitelistTypeEnum.API }
]

// 启用状态选项
export const enableStatusOptions = [
  { label: '禁用', value: EnableStatusEnum.DISABLED },
  { label: '启用', value: EnableStatusEnum.ENABLED }
]
