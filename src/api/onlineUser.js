import request from '@/utils/request'

/**
 * 分页查询在线用户列表
 * @param {Object} params 查询参数
 * @param {number} params.current 页码
 * @param {number} params.size 每页大小
 * @param {string} params.keyword 搜索关键词，支持用户ID或用户名模糊搜索
 * @param {string} params.deviceType 设备类型筛选（PC、WEB、APP、MOBILE等）
 * @param {boolean} params.sortType 排序类型，true为升序，false为降序
 * @returns {Promise} 分页查询结果
 */
export function getOnlineUserList(params) {
  return request({
    url: '/online-user/list',
    method: 'get',
    params
  })
}

// 注意：getUserTerminals 接口已被移除，终端信息现在包含在用户列表接口的响应中

/**
 * 强制注销用户
 * @param {string} loginId 用户登录ID
 * @param {string} deviceType 设备类型（可选）
 * @returns {Promise} 注销结果
 */
export function logoutUser(loginId, deviceType) {
  return request({
    url: '/online-user/logout',
    method: 'post',
    params: {
      loginId,
      ...(deviceType && { deviceType })
    }
  })
}

/**
 * 踢出用户
 * @param {string} loginId 用户登录ID
 * @param {string} deviceType 设备类型（可选）
 * @returns {Promise} 踢出结果
 */
export function kickoutUser(loginId, deviceType) {
  return request({
    url: '/online-user/kickout',
    method: 'post',
    params: {
      loginId,
      ...(deviceType && { deviceType })
    }
  })
}

/**
 * 批量踢出用户
 * @param {Object} data 批量操作数据
 * @param {Array<string>} data.loginIds 用户ID列表
 * @param {string} data.deviceType 设备类型（可选）
 * @param {string} data.operationType 操作类型：LOGOUT（强制注销）、KICKOUT（踢出）
 * @param {string} data.reason 操作原因（可选）
 * @returns {Promise} 批量操作结果
 */
export function batchKickoutUsers(data) {
  return request({
    url: '/online-user/batch-kickout',
    method: 'post',
    data
  })
}

/**
 * 根据Token MD5踢出用户
 * @param {string} tokenMd5 Token的MD5哈希值
 * @returns {Promise} 踢出结果
 */
export function kickoutUserByTokenMd5(tokenMd5) {
  return request({
    url: `/online-user/token-md5/${tokenMd5}`,
    method: 'delete'
  })
}

/**
 * 获取在线用户统计信息
 * @returns {Promise} 统计信息
 */
export function getOnlineUserStatistics() {
  return request({
    url: '/online-user/statistics',
    method: 'get'
  })
}

/**
 * 检查用户在线状态
 * @param {string} loginId 用户登录ID
 * @returns {Promise} 在线状态
 */
export function checkUserOnlineStatus(loginId) {
  return request({
    url: `/online-user/${loginId}/online-status`,
    method: 'get'
  })
}

/**
 * 获取用户在线设备数量
 * @param {string} loginId 用户登录ID
 * @returns {Promise} 设备数量
 */
export function getUserDeviceCount(loginId) {
  return request({
    url: `/online-user/${loginId}/device-count`,
    method: 'get'
  })
}

// 注意：设备类型是动态的，由统计信息中的deviceTypeStatistics提供
// 不再使用固定的设备类型枚举

// 在线状态枚举
export const OnlineStatusEnum = {
  ONLINE: 'ONLINE',
  IDLE: 'IDLE',
  OFFLINE: 'OFFLINE'
}

// 操作类型枚举
export const OperationTypeEnum = {
  LOGOUT: 'LOGOUT',
  KICKOUT: 'KICKOUT'
}

// 设备类型选项将从统计信息中动态生成
// 不再使用固定的设备类型选项

// 在线状态选项
export const onlineStatusOptions = [
  { label: '在线', value: OnlineStatusEnum.ONLINE },
  { label: '空闲', value: OnlineStatusEnum.IDLE },
  { label: '离线', value: OnlineStatusEnum.OFFLINE }
]

// 操作类型选项
export const operationTypeOptions = [
  { label: '强制注销', value: OperationTypeEnum.LOGOUT },
  { label: '踢人下线', value: OperationTypeEnum.KICKOUT }
]
