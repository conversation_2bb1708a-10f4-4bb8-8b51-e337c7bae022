import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { usePermissionStore } from '@/stores/permission'
import { useMenuStore } from '@/stores/menu'
import { checkRoutePermission } from '@/utils/permission'
import { addDynamicRoutes, isPathMatch } from '@/utils/dynamicRoutes'
import { addVisitRecord } from '@/utils/visitHistory'

// 路由配置
const routes = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: {
      title: '用户登录'
    }
  },
  {
    path: '/session-expired',
    name: 'SessionExpired',
    component: () => import('@/views/auth/SessionExpired.vue'),
    meta: {
      title: '登录已失效'
    }
  },
  {
    path: '/403',
    name: 'Forbidden',
    component: () => import('@/views/403/Forbidden.vue'),
    meta: {
      title: '访问被拒绝'
    }
  },
  {
    path: '/system-error',
    name: 'SystemError',
    component: () => import('@/views/error/SystemError.vue'),
    meta: {
      title: '系统异常'
    }
  },
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('@/views/404/NotFound.vue'),
    meta: {
      title: '页面未找到'
    }
  },
  // 主布局路由 - 动态路由将作为此路由的子路由添加
  {
    path: '/',
    name: 'Layout',
    component: () => import('@/components/Layout.vue'),
    meta: {
     
    },
    children: [
      // 默认仪表盘路由
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard.vue'),
        meta: {
          title: '仪表盘',
          breadcrumb: '仪表盘',
          icon: 'all-application',
          affix: true, // 固定标签，不可关闭
          keepAlive: true
        }
      },
      // 重定向路由（用于页面刷新）
      {
        path: 'redirect/:path(.*)',
        name: 'Redirect',
        component: () => import('@/views/Redirect.vue'),
        meta: {
          hidden: true
        }
      },
      // 个人中心路由
      {
        path: 'profile',
        name: 'ProfileCenter',
        component: () => import('@/views/profile/ProfileCenter.vue'),
        meta: {
          title: '个人中心',
          breadcrumb: '个人中心',
          hidden: true // 不在菜单中显示
        }
      }
    ]
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    // 路由切换时的滚动行为
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 全局前置守卫
router.beforeEach(async (to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - aKey`
  }

  // 定义白名单路由（不需要登录的页面）
  const whiteList = ['/login', '/session-expired', '/403', '/system-error', '/404']

  // 检查是否在白名单中，如果是则直接放行
  if (whiteList.some(pattern => isPathMatch(pattern, to.path))) {
    next()
    return
  }

  // 获取认证状态
  const authStore = useAuthStore()

  if (authStore.isLoggedIn()) {
    // === 已登录用户处理逻辑 ===

    // 获取权限、菜单和用户信息store
    const permissionStore = usePermissionStore()
    const menuStore = useMenuStore()

    // 如果权限、菜单或用户信息未加载，先加载数据
    if (!permissionStore.isLoaded || !menuStore.isLoaded || !authStore.userInfoLoaded) {
      try {
        // 并行获取权限、菜单和用户信息数据
        const promises = []

        if (!permissionStore.isLoaded) {
          promises.push(permissionStore.fetchPermissions())
        }

        if (!menuStore.isLoaded) {
          promises.push(menuStore.fetchMenus())
        }

        if (!authStore.userInfoLoaded) {
          promises.push(authStore.fetchUserInfo())
        }

        await Promise.all(promises)
      } catch (error) {
        // 获取失败，跳转到系统异常页面
        const errorMessage = error.message || error.toString()
        next(`/system-error?error=${encodeURIComponent(errorMessage)}`)
        return
      }
    }
   // 如果菜单已加载但动态路由未添加，添加动态路由
    if (menuStore.isLoaded && menuStore.menus.length > 0 && !menuStore.routesAdded) {
      addDynamicRoutes(router, menuStore.menus)
      menuStore.markRoutesAdded()
      next({ path: to.path, query: to.query, hash: to.hash, replace: true })
      return
    }
    
    // 只有在动态路由已添加的情况下才检查路由是否存在
    // 这样可以避免在动态路由加载前产生 "No match found" 警告
    if (menuStore.routesAdded) {
      const routeExists = router.hasRoute(to.name) || router.resolve(to.path).matched.length > 0

      if (!routeExists) {
        console.warn(`路由不存在: ${to.path}`, {
          name: to.name,
          path: to.path,
          hasRoute: router.hasRoute(to.name),
          resolveMatched: router.resolve(to.path).matched.length
        })
        // 路由不存在，跳转到404页面
        next('/404')
        return
      }
    }

    // 检查路由权限
    if (!checkRoutePermission(to)) {
      // 没有权限，跳转到403页面
      next('/403')
      return
    }

    // 所有检查通过，允许访问
    next()

  } else {
    // === 未登录用户处理逻辑 ===

    // 不在白名单中，需要登录
    // 检查是否曾经成功登录过（通过专门的标记位）
    const hasLoginHistory = localStorage.getItem('hasLoginHistory') === 'true'

    // 保存当前路径用于登录后重定向
    const fullPath = to.fullPath
    authStore.setRedirectPath(fullPath)

    if (hasLoginHistory) {
      // 曾经成功登录过，现在token失效，跳转到登录失效页面
      next('/session-expired?reason=expired')
    } else {
      // 从未登录过，跳转到登录失效页面并标记为未授权
      next('/session-expired?reason=unauthorized')
    }
  }
})

// 全局后置钩子
router.afterEach((to) => {
  // 记录访问历史（只记录有菜单的页面）
  if (to.meta && to.meta.title && to.path !== '/login' && to.path !== '/session-expired' && !to.path.startsWith('/40') && !to.path.startsWith('/50')) {
    try {
      const authStore = useAuthStore()
      const menuStore = useMenuStore()

      // 只有登录用户才记录访问历史
      if (authStore && authStore.isLoggedIn && authStore.isLoggedIn()) {
        // 查找对应的菜单项
        const findMenuByPath = (menus, path, parentPath = '') => {
          if (!menus || !Array.isArray(menus)) {
            return null
          }

          for (const menu of menus) {
            if (menu && menu.routePath) {
              // 构建完整路径：父路径 + 当前菜单路径
              const fullPath = parentPath + menu.routePath

              // 直接匹配完整路径
              if (fullPath === path) {
                return menu
              }

              // 如果当前菜单有子菜单，递归查找
              if (menu.children && Array.isArray(menu.children) && menu.children.length > 0) {
                // 传递当前菜单路径作为父路径
                const found = findMenuByPath(menu.children, path, fullPath)
                if (found) return found
              }
            }
          }
          return null
        }

        const menuList = (menuStore && menuStore.menus) ? menuStore.menus : []
        const menuItem = findMenuByPath(menuList, to.path)
        if (menuItem) {
          addVisitRecord({
            path: to.path,
            name: menuItem.menuName || to.meta.title,
            icon: menuItem.icon || 'Document',
            title: to.meta.title
          })
        } else if (to.meta && to.meta.title) {
          // 如果没找到菜单项，但有页面标题，也记录下来
          addVisitRecord({
            path: to.path,
            name: to.meta.title,
            icon: 'Document',
            title: to.meta.title
          })
        }
      }
    } catch (error) {
      console.warn('记录访问历史失败:', error)
    }
  }
})

export default router
