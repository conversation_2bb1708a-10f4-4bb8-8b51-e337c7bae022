/**
 * 组件路径映射配置
 * 统一管理所有页面组件的路径和描述
 */

export const componentConfig = {
  // 用户管理模块
  'UserManagement': {
    path: '@/views/user/UserManagement.vue',
    import: () => import('@/views/user/UserManagement.vue'),
    description: '用户管理页面'
  },
  'UserTypeManagement': {
    path: '@/views/user/UserTypeManagement.vue',
    import: () => import('@/views/user/UserTypeManagement.vue'),
    description: '用户类型管理页面'
  },

  // 菜单管理模块
  'MenuManagement': {
    path: '@/views/menu/MenuManagement.vue',
    import: () => import('@/views/menu/MenuManagement.vue'),
    description: '菜单管理页面'
  },

  // 日志管理模块
  'LoginLogManagement': {
    path: '@/views/log/LoginLogManagement.vue',
    import: () => import('@/views/log/LoginLogManagement.vue'),
    description: '登录日志管理页面'
  },
  'LoginLogAnalytics': {
    path: '@/views/log/LoginLogAnalytics.vue',
    import: () => import('@/views/log/LoginLogAnalytics.vue'),
    description: '登录日志统计分析页面'
  },
  'OperationLogManagement': {
    path: '@/views/log/OperationLogManagement.vue',
    import: () => import('@/views/log/OperationLogManagement.vue'),
    description: '操作日志管理页面'
  },

  // 安全管理模块
  'IpWhitelist': {
    path: '@/views/security/IpWhitelist.vue',
    import: () => import('@/views/security/IpWhitelist.vue'),
    description: 'IP白名单管理页面'
  },
  'OnlineUserManagement': {
    path: '@/views/security/OnlineUserManagement.vue',
    import: () => import('@/views/security/OnlineUserManagement.vue'),
    description: '在线用户管理页面'
  },

  // 系统监控模块
  'SystemResourceMonitor': {
    path: '@/views/system/SystemResourceMonitor.vue',
    import: () => import('@/views/system/SystemResourceMonitor.vue'),
    description: '系统资源监控页面'
  },

  // 错误页面
  'Forbidden': {
    path: '@/views/403/Forbidden.vue',
    import: () => import('@/views/403/Forbidden.vue'),
    description: '403禁止访问页面'
  },
  'NotFound': {
    path: '@/views/404/NotFound.vue',
    import: () => import('@/views/404/NotFound.vue'),
    description: '404页面未找到'
  },
  'SystemError': {
    path: '@/views/error/SystemError.vue',
    import: () => import('@/views/error/SystemError.vue'),
    description: '系统错误页面'
  },

  // 认证页面
  'SessionExpired': {
    path: '@/views/auth/SessionExpired.vue',
    import: () => import('@/views/auth/SessionExpired.vue'),
    description: '会话过期页面'
  },

  // 其他页面
  'Login': {
    path: '@/views/Login.vue',
    import: () => import('@/views/Login.vue'),
    description: '登录页面'
  },
  'Redirect': {
    path: '@/views/Redirect.vue',
    import: () => import('@/views/Redirect.vue'),
    description: '重定向页面'
  },
  'Dashboard': {
    path: '@/views/Dashboard.vue',
    import: () => import('@/views/Dashboard.vue'),
    description: '仪表盘页面'
  },
  'IframeView': {
    path: '@/views/IframeView.vue',
    import: () => import('@/views/IframeView.vue'),
    description: '外部链接页面'
  }
}

/**
 * 获取组件导入函数 (通过组件名称)
 * @param {string} name 组件名称
 * @returns {Function} 组件导入函数
 */
export const getComponentImport = (name) => {
  return componentConfig[name]?.import
}

/**
 * 获取组件导入函数 (通过路径)
 * @param {string} path 组件路径
 * @returns {Function} 组件导入函数
 */
export const getComponentImportByPath = (path) => {
  const component = Object.values(componentConfig).find(config => config.path === path)
  return component?.import
}

/**
 * 获取组件描述
 * @param {string} name 组件名称
 * @returns {string} 组件描述
 */
export const getComponentDescription = (name) => {
  return componentConfig[name]?.description || '自定义组件'
}

/**
 * 获取组件路径
 * @param {string} name 组件名称
 * @returns {string} 组件路径
 */
export const getComponentPath = (name) => {
  return componentConfig[name]?.path
}

/**
 * 获取所有可用的组件名称
 * @returns {Array} 组件名称列表
 */
export const getAvailableComponents = () => {
  return Object.keys(componentConfig).sort()
}

/**
 * 获取所有组件及其描述
 * @returns {Object} 组件名称和描述的映射
 */
export const getAllComponentsWithDescriptions = () => {
  const result = {}
  Object.entries(componentConfig).forEach(([name, config]) => {
    result[name] = config.description
  })
  return result
}

/**
 * 获取所有组件的完整信息
 * @returns {Object} 组件配置对象
 */
export const getAllComponents = () => {
  return componentConfig
}
