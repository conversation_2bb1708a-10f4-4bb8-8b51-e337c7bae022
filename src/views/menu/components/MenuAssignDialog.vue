<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`为「${userType.typeName}」分配菜单权限`"
    :width="dialogWidth"
    :close-on-click-modal="false"
    append-to-body
    class="menu-assign-dialog"
    @close="handleClose"
  >
    <div class="menu-assign-content">
      <!-- 操作按钮 -->
      <div class="action-bar">
        <el-button size="small" @click="handleExpandAll">
          <el-icon><Plus /></el-icon>
          展开全部
        </el-button>
        <el-button size="small" @click="handleCollapseAll">
          <el-icon><Minus /></el-icon>
          收起全部
        </el-button>
        <el-button size="small" @click="handleCheckAll">
          <el-icon><Check /></el-icon>
          全选
        </el-button>
        <el-button size="small" @click="handleUncheckAll">
          <el-icon><Close /></el-icon>
          取消全选
        </el-button>
      </div>

      <!-- 菜单树 -->
      <div class="menu-tree-container">
        <el-tree
          ref="treeRef"
          v-loading="loading"
          :data="menuTreeData"
          :props="treeProps"
          show-checkbox
          node-key="id"
          :check-strictly="checkStrictly"
          :expand-on-click-node="false"
          :default-expand-all="true"
          class="menu-tree"
          @check="handleNodeCheck"
        >
          <template #default="{ node, data }">
            <div class="tree-node">
              <svg-icon
                v-if="data.icon"
                :name="data.icon"
                class="node-icon"
              />
              <el-icon v-else class="node-icon">
                <Document />
              </el-icon>
              <span class="node-label">{{ node.label }}</span>
              <el-tag
                v-if="data.menuType === 1"
                type="info"
                size="small"
                class="node-tag"
              >
                目录
              </el-tag>
              <el-tag
                v-else-if="data.menuType === 2"
                type="success"
                size="small"
                class="node-tag"
              >
                菜单
              </el-tag>
              <el-tag
                v-else-if="data.menuType === 3"
                type="warning"
                size="small"
                class="node-tag"
              >
                按钮
              </el-tag>
            </div>
          </template>
        </el-tree>
      </div>

      <!-- 统计信息 -->
      <div class="statistics">
        <el-text type="info" size="small">
          已选择 {{ checkedCount }} 个菜单项
        </el-text>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :loading="saving"
          @click="handleSubmit"
        >
          保存
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, computed, nextTick, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Minus, Check, Close, Document } from '@element-plus/icons-vue'
import { getUserTypeMenus, assignUserTypeMenus } from '@/api/userType'
import SvgIcon from '@/components/SvgIcon.vue'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  userType: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const saving = ref(false)
const treeRef = ref()
const menuTreeData = ref([])
const checkedMenuIds = ref([])
const isMobile = ref(false)
const checkStrictly = ref(true) // 控制父子联动

// 树形组件配置
const treeProps = {
  children: 'children',
  label: 'menuName',
  disabled: 'disabled'
}

// 检查是否为移动端
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

// 计算属性
const dialogWidth = computed(() => {
  return isMobile.value ? '95%' : '600px'
})

// 计算已选择的菜单数量
const checkedCount = computed(() => {
  if (!treeRef.value) return 0
  return treeRef.value.getCheckedKeys().length
})

// 生命周期
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})

// 监听 visible 变化
watch(
  () => props.visible,
  (newVal) => {
    dialogVisible.value = newVal
    if (newVal && props.userType && props.userType.id) {
      initData()
    }
  },
  { immediate: true }
)

// 监听 userType 变化
watch(
  () => props.userType,
  (newVal) => {
    if (dialogVisible.value && newVal && newVal.id) {
      initData()
    }
  },
  { deep: true }
)

// 监听 dialogVisible 变化
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// 初始化数据
const initData = async () => {
  await fetchMenuData()
}

// 获取菜单数据和用户类型已分配的菜单
const fetchMenuData = async () => {
  try {
    loading.value = true

    const response = await getUserTypeMenus(props.userType.id)

    // 直接从响应中获取数据（响应拦截器已处理）
    const allMenus = response.allMenus || []
    const menus = response.menus || []

    // 构建菜单树结构
    if (allMenus.length > 0) {
      menuTreeData.value = buildMenuTree(allMenus)

      // 确保 menus 是已拥有的权限ID数组
      const ownedMenuIds = Array.isArray(menus) ? menus : []
      checkedMenuIds.value = ownedMenuIds

      // 等待树组件渲染完成后设置选中状态
      await nextTick()
      if (treeRef.value && ownedMenuIds.length > 0) {
        // 先禁用父子联动，精确设置选中状态
        checkStrictly.value = true
        await nextTick()

        // 直接设置选中的节点ID
        treeRef.value.setCheckedKeys(ownedMenuIds)

        // 然后启用父子联动
        await nextTick()
        checkStrictly.value = false
      } else {
        // 如果没有选中的菜单，直接启用父子联动
        checkStrictly.value = false
      }
    } else {
      ElMessage.warning('没有可分配的菜单数据')
    }
  } catch (error) {
    // 错误提示由响应拦截器处理，这里只需要记录日志
    console.error('获取菜单数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 构建菜单树结构
const buildMenuTree = (menuList) => {
  if (!Array.isArray(menuList) || menuList.length === 0) {
    return []
  }

  const menuMap = new Map()
  const rootMenus = []

  // 先将所有菜单放入Map中
  menuList.forEach(menu => {
    if (menu && menu.id) {
      menuMap.set(menu.id, { ...menu, children: [] })
    }
  })

  // 构建树形结构
  menuList.forEach(menu => {
    if (!menu || !menu.id) return

    const menuItem = menuMap.get(menu.id)
    if (menu.parentId && menuMap.has(menu.parentId)) {
      // 有父菜单，添加到父菜单的children中
      const parentMenu = menuMap.get(menu.parentId)
      parentMenu.children.push(menuItem)
    } else {
      // 没有父菜单或父菜单不存在，作为根菜单
      rootMenus.push(menuItem)
    }
  })

  // 递归排序菜单
  const sortMenus = (menus) => {
    menus.sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0))
    menus.forEach(menu => {
      if (menu.children && menu.children.length > 0) {
        sortMenus(menu.children)
      }
    })
  }

  sortMenus(rootMenus)
  return rootMenus
}

// 展开全部
const handleExpandAll = () => {
  if (treeRef.value) {
    const allKeys = getAllNodeKeys(menuTreeData.value)
    allKeys.forEach(key => {
      treeRef.value.store.nodesMap[key]?.expand()
    })
  }
}

// 收起全部
const handleCollapseAll = () => {
  if (treeRef.value) {
    const allKeys = getAllNodeKeys(menuTreeData.value)
    allKeys.forEach(key => {
      treeRef.value.store.nodesMap[key]?.collapse()
    })
  }
}

// 全选
const handleCheckAll = () => {
  if (treeRef.value) {
    const allKeys = getAllNodeKeys(menuTreeData.value)
    treeRef.value.setCheckedKeys(allKeys)
  }
}

// 取消全选
const handleUncheckAll = () => {
  if (treeRef.value) {
    treeRef.value.setCheckedKeys([])
  }
}

// 获取所有节点的key
const getAllNodeKeys = (nodes) => {
  const keys = []
  const traverse = (nodeList) => {
    nodeList.forEach(node => {
      keys.push(node.id)
      if (node.children && node.children.length > 0) {
        traverse(node.children)
      }
    })
  }
  traverse(nodes)
  return keys
}




// 处理节点选中事件
const handleNodeCheck = () => {
  // 由于我们已经设置了 check-strictly="false"，Element Plus 会自动处理父子联动
  // 这里预留给未来可能的自定义逻辑
}

// 提交保存
const handleSubmit = async () => {
  try {
    saving.value = true

    // 获取选中的菜单ID
    const checkedKeys = treeRef.value.getCheckedKeys()
    const halfCheckedKeys = treeRef.value.getHalfCheckedKeys()
    const menuIds = [...checkedKeys, ...halfCheckedKeys]

    await assignUserTypeMenus(props.userType.id, { menuIds })

    ElMessage.success('权限分配成功')
    emit('success')
  } catch (error) {
    // 错误提示由响应拦截器处理，这里只需要记录日志
    console.error('分配权限失败:', error)
  } finally {
    saving.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  // 清理数据
  menuTreeData.value = []
  checkedMenuIds.value = []
  checkStrictly.value = true // 重置为严格模式
}
</script>

<style scoped>
.menu-assign-content {
  max-height: 500px;
}

.action-bar {
  display: flex;
  gap: 8px;
  margin-top: 16px;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--border-color-light);
}

.menu-tree-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid var(--border-color-light);
  border-radius: 6px;
  padding: 8px;
}

.menu-tree {
  background: transparent;
}

.tree-node {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  padding-right: 8px;
}

.node-icon {
  font-size: 16px;
  color: var(--text-color-secondary);
  flex-shrink: 0;
}

.node-label {
  flex: 1;
  font-size: 14px;
}

.node-tag {
  flex-shrink: 0;
}

.statistics {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid var(--border-color-light);
  text-align: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .menu-assign-content {
    max-height: 60vh;
  }

  .action-bar {
    flex-wrap: wrap;
    gap: 6px;
    margin-top: 12px;
    margin-bottom: 12px;
  }

  .action-bar .el-button {
    font-size: 12px;
    padding: 6px 12px;
  }

  .menu-tree-container {
    max-height: 40vh;
  }

  .dialog-footer {
    padding-top: 16px;
    gap: 8px;
  }

  .dialog-footer .el-button {
    flex: 1;
    min-width: 80px;
  }
}

/* 超小屏幕适配 */
@media (max-width: 480px) {
  .menu-assign-content {
    max-height: 70vh;
  }

  .action-bar {
    gap: 4px;
    margin-top: 8px;
    margin-bottom: 8px;
  }

  .action-bar .el-button {
    font-size: 11px;
    padding: 4px 8px;
    flex: 1;
  }

  .menu-tree-container {
    max-height: 50vh;
    padding: 6px;
  }

  .dialog-footer {
    flex-direction: column-reverse;
    gap: 12px;
  }

  .dialog-footer .el-button {
    width: 100%;
  }

  .statistics {
    font-size: 12px;
  }
}
</style>

<!-- 全局样式 -->
<style>
/* 对话框整体样式优化 */
.menu-assign-dialog .el-dialog {
  margin: 10vh auto 50px;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.menu-assign-dialog .el-dialog__header {
  padding: 20px 20px 0 20px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.menu-assign-dialog .el-dialog__title {
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.menu-assign-dialog .el-dialog__body {
  padding: 0 20px;
}

.menu-assign-dialog .el-dialog__footer {
  padding: 0 20px 20px 20px;
  border-top: 1px solid var(--el-border-color-lighter);
}

/* 移动端对话框适配 */
@media (max-width: 768px) {
  .menu-assign-dialog .el-dialog {
    margin: 5vh auto 20px;
    width: 95% !important;
    max-width: none;
  }

  .menu-assign-dialog .el-dialog__header {
    padding: 16px 16px 0 16px;
  }

  .menu-assign-dialog .el-dialog__body {
    padding: 0 16px;
  }

  .menu-assign-dialog .el-dialog__footer {
    padding: 0 16px 16px 16px;
  }

  .menu-assign-dialog .el-dialog__title {
    font-size: 16px;
  }
}

/* 超小屏幕对话框适配 */
@media (max-width: 480px) {
  .menu-assign-dialog .el-dialog {
    margin: 2vh auto 10px;
    width: 98% !important;
  }

  .menu-assign-dialog .el-dialog__header {
    padding: 12px 12px 0 12px;
  }

  .menu-assign-dialog .el-dialog__body {
    padding: 0 12px;
  }

  .menu-assign-dialog .el-dialog__footer {
    padding: 0 12px 12px 12px;
  }

  .menu-assign-dialog .el-dialog__title {
    font-size: 14px;
    line-height: 1.4;
  }
}
</style>
