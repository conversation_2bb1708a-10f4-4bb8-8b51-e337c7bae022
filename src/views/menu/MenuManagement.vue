<template>
  <div class="management-page">
    <!-- 搜索和操作区域 -->
    <div class="search-section">
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="菜单名称">
          <el-input
            v-model="searchForm.menuName"
            placeholder="请输入菜单名称"
            clearable
            class="search-input"
          />
        </el-form-item>
        <el-form-item label="菜单类型">
          <el-select
            v-model="searchForm.menuType"
            placeholder="请选择菜单类型"
            clearable
            class="search-select"
          >
            <el-option label="目录" :value="1" />
            <el-option label="菜单" :value="2" />
            <el-option label="按钮" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="菜单状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            class="search-select"
          >
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>

      <div class="action-buttons">
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          新增菜单
        </el-button>
        <el-button 
          type="success" 
          :disabled="!hasSelection"
          @click="handleBatchEnable"
        >
          <el-icon><Check /></el-icon>
          批量启用
        </el-button>
        <el-button 
          type="warning" 
          :disabled="!hasSelection"
          @click="handleBatchDisable"
        >
          <el-icon><Close /></el-icon>
          批量禁用
        </el-button>
        <el-button @click="handleExpandAll">
          <el-icon><Expand /></el-icon>
          {{ isExpandAll ? '收起全部' : '展开全部' }}
        </el-button>
      </div>
    </div>

    <!-- 菜单表格 -->
    <div class="table-section">
      <el-table
        ref="menuTableRef"
        v-loading="loading"
        :data="menuList"
        row-key="id"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :default-expand-all="isExpandAll"
        @selection-change="handleSelectionChange"
        :header-cell-style="{ background: 'var(--bg-color-table-header)', color: 'var(--text-color-primary)', fontWeight: '600' }"
        :row-style="{ height: '56px' }"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="menuName" label="菜单名称" min-width="250" show-overflow-tooltip>
          <template  #default="{ row }">
            <div class="menu-name-cell">
              <div class="menu-icon-wrapper" v-if="row.icon">
                <svg-icon
                  :name="row.icon"
                  class="menu-icon"
                />

              </div>
              <span class="menu-name">{{ row.menuName }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="sortOrder" label="排序" width="80" />
        <el-table-column prop="menuType" label="类型" width="80">
          <template #default="{ row }">
            <el-tag :type="getMenuTypeTagType(row.menuType)">
              {{ getMenuTypeText(row.menuType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="menuCode" label="菜单编码" width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <el-tag size="small" type="info" effect="plain">{{ row.menuCode }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="routePath" label="路由路径" min-width="160" show-overflow-tooltip>
          <template #default="{ row }">
            <span class="route-path">{{ row.routePath || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="componentPath" label="组件路径" min-width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <span class="component-path">{{ row.componentPath || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="permission" label="权限标识" width="140" show-overflow-tooltip>
          <template #default="{ row }">
            <el-tag v-if="row.permission" size="small" type="warning" effect="plain">
              {{ row.permission }}
            </el-tag>
            <span v-else class="text-gray">-</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="visible" label="可见" width="80" align="center">
          <template #default="{ row }">
            <el-tag
              :type="row.visible === 1 ? 'success' : 'info'"
              size="small"
              effect="light"
            >
              {{ row.visible === 1 ? '显示' : '隐藏' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="80" align="center">
          <template #default="{ row }">
            <el-switch
              v-model="row.status"
              :active-value="1"
              :inactive-value="0"
              @change="handleStatusChange(row)"
              size="small"
              :active-color="'var(--success-color)'"
              :inactive-color="'var(--border-color-dark)'"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="220" fixed="right" align="center">
          <template #default="{ row }">
            <div class="action-buttons-row">
              <el-tooltip content="编辑" placement="top">
                <el-button
                  type="primary"
                  size="small"
                  @click="handleEdit(row)"
                  :icon="Edit"
                  circle
                />
              </el-tooltip>
              <el-tooltip content="新增子菜单" placement="top">
                <el-button
                  type="success"
                  size="small"
                  @click="handleAddChild(row)"
                  :icon="Plus"
                  circle
                />
              </el-tooltip>
              <el-tooltip content="删除" placement="top">
                <el-button
                  type="danger"
                  size="small"
                  @click="handleDelete(row)"
                  :icon="Delete"
                  circle
                />
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 新增/编辑菜单弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="menuFormRef"
        :model="menuForm"
        :rules="menuFormRules"
        label-width="120px"
      >
        <!-- 基础信息 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item prop="parentId">
              <template #label>
                <el-tooltip
                  content="选择上级菜单，留空则为顶级菜单。"
                  placement="top"
                >
                  <el-icon class="form-label-tip"><QuestionFilled /></el-icon>
                </el-tooltip>
                <span>父级菜单</span>
              </template>
              <el-tree-select
                v-model="menuForm.parentId"
                :data="parentMenuOptions"
                :props="{ value: 'id', label: 'menuName', children: 'children' }"
                placeholder="请选择父级菜单（留空为顶级菜单）"
                clearable
                check-strictly
                :render-after-expand="false"
                filterable
                :empty-text="parentMenuOptions.length === 0 ? '暂无可选的父菜单' : '无匹配数据'"
              >
                <template #default="{ data }">
                  <span class="tree-select-option">
                    <span>{{ data.menuName }}</span>
                    <el-tag
                      :type="data.menuType === 1 ? 'info' : 'success'"
                      size="small"
                      class="menu-type-tag"
                    >
                      {{ data.menuType === 1 ? '目录' : '菜单' }}
                    </el-tag>
                  </span>
                </template>
              </el-tree-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="menuType">
              <template #label>
                <el-tooltip
                  content="目录：用于分组；菜单：可访问页面；按钮：页面内操作"
                  placement="top"
                >
                  <el-icon class="form-label-tip"><QuestionFilled /></el-icon>
                </el-tooltip>
                <span>菜单类型</span>
              </template>
              <el-radio-group v-model="menuForm.menuType">
                <el-radio :value="1">目录</el-radio>
                <el-radio :value="2">菜单</el-radio>
                <el-radio :value="3">按钮</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 名称和编码 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item prop="menuName">
              <template #label>
                <el-tooltip
                  content="显示在导航菜单中的名称"
                  placement="top"
                >
                  <el-icon class="form-label-tip"><QuestionFilled /></el-icon>
                </el-tooltip>
                <span>菜单名称</span>
              </template>
              <el-input v-model="menuForm.menuName" placeholder="请输入菜单名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="menuCode">
              <template #label>
                <el-tooltip
                  content="唯一标识，用于系统内部识别"
                  placement="top"
                >
                  <el-icon class="form-label-tip"><QuestionFilled /></el-icon>
                </el-tooltip>
                <span>菜单编码</span>
              </template>
              <el-input v-model="menuForm.menuCode" placeholder="请输入菜单编码">
                <template #append>
                  <el-tooltip content="生成随机编码" placement="top">
                    <el-button
                      
                      @click="generateMenuCode"
                      class="generate-code-btn"
                      :icon="Refresh"
                    >
                    </el-button>
                  </el-tooltip>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 路由配置（目录和菜单） -->
        <template v-if="menuForm.menuType !== 3">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item prop="routePath">
                <template #label>
                  <el-tooltip
                    :content="menuForm.externalLink === 1 ? '选择外部链接时路由地址需要以 http(s):// 开头' : '页面访问路径，必须以 / 开头'"
                    placement="top"
                  >
                    <el-icon class="form-label-tip"><QuestionFilled /></el-icon>
                  </el-tooltip>
                  <span>路由路径</span>
                </template>
                <el-input
                  v-model="menuForm.routePath"
                  :placeholder="menuForm.externalLink === 1 ? '请输入完整网址，如：https://www.example.com' : '请输入路由路径，如：/user/list'"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="menuForm.menuType === 2">
              <el-form-item prop="componentPath">
                <template #label>
                  <el-tooltip
                    content="Vue组件文件路径，如：@/views/user/UserList.vue"
                    placement="top"
                  >
                    <el-icon class="form-label-tip"><QuestionFilled /></el-icon>
                  </el-tooltip>
                  <span>组件路径</span>
                </template>
                <ComponentSelector v-model="menuForm.componentPath" />
              </el-form-item>
            </el-col>
          </el-row>
        </template>

        <!-- 图标和排序 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item prop="icon">
              <template #label>
                <el-tooltip
                  content="菜单显示的图标"
                  placement="top"
                >
                  <el-icon class="form-label-tip"><QuestionFilled /></el-icon>
                </el-tooltip>
                <span>图标</span>
              </template>
              <IconSelector v-model="menuForm.icon" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="sortOrder">
              <template #label>
                <el-tooltip
                  content="数字越小排序越靠前"
                  placement="top"
                >
                  <el-icon class="form-label-tip"><QuestionFilled /></el-icon>
                </el-tooltip>
                <span>排序号</span>
              </template>
              <el-input-number
                v-model="menuForm.sortOrder"
                :min="0"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 权限和可见性 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item prop="permission">
              <template #label>
                <el-tooltip
                  content="用于权限控制，格式：模块:操作"
                  placement="top"
                >
                  <el-icon class="form-label-tip"><QuestionFilled /></el-icon>
                </el-tooltip>
                <span>权限标识</span>
              </template>
              <el-input v-model="menuForm.permission" placeholder="如：user:list" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="visible">
              <template #label>
                <el-tooltip
                  content="隐藏后不在菜单中显示，但仍可访问"
                  placement="top"
                >
                  <el-icon class="form-label-tip"><QuestionFilled /></el-icon>
                </el-tooltip>
                <span>是否可见</span>
              </template>
              <el-radio-group v-model="menuForm.visible">
                <el-radio :value="1">显示</el-radio>
                <el-radio :value="0">隐藏</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 状态和外链配置 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item prop="status">
              <template #label>
                <el-tooltip
                  content="禁用后无法访问该菜单"
                  placement="top"
                >
                  <el-icon class="form-label-tip"><QuestionFilled /></el-icon>
                </el-tooltip>
                <span>是否启用</span>
              </template>
              <el-radio-group v-model="menuForm.status">
                <el-radio :value="1">启用</el-radio>
                <el-radio :value="0">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="menuForm.menuType === 2">
            <el-form-item prop="externalLink">
              <template #label>
                <el-tooltip
                  content="是否为外部网站链接"
                  placement="top"
                >
                  <el-icon class="form-label-tip"><QuestionFilled /></el-icon>
                </el-tooltip>
                <span>外部链接</span>
              </template>
              <el-radio-group v-model="menuForm.externalLink">
                <el-radio :value="0">否</el-radio>
                <el-radio :value="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 外链打开方式 -->
        <el-row :gutter="20" v-if="menuForm.menuType === 2 && menuForm.externalLink === 1">
          <el-col :span="12">
            <el-form-item prop="openMode">
              <template #label>
                <el-tooltip
                  content="外部链接的打开方式"
                  placement="top"
                >
                  <el-icon class="form-label-tip"><QuestionFilled /></el-icon>
                </el-tooltip>
                <span>打开方式</span>
              </template>
              <el-radio-group v-model="menuForm.openMode">
                <el-radio value="_self">当前窗口</el-radio>
                <el-radio value="_blank">新窗口</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <!-- 占位，保持布局 -->
          </el-col>
        </el-row>

        <!-- 路由参数 -->
        <el-form-item prop="routeParams" v-if="menuForm.menuType === 2">
          <template #label>
            <el-tooltip
              content="页面路由的查询参数，JSON格式"
              placement="top"
            >
              <el-icon class="form-label-tip"><QuestionFilled /></el-icon>
            </el-tooltip>
            <span>路由参数</span>
          </template>
          <RouteParamsEditor ref="routeParamsEditorRef" v-model="menuForm.routeParams" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search, Refresh, Plus, Check, Close, Expand,
  Edit, Delete, QuestionFilled
} from '@element-plus/icons-vue'
import { useMenuManagementStore } from '@/stores/menuManagement'
import IconSelector from '@/components/IconSelector.vue'
import ComponentSelector from '@/components/ComponentSelector.vue'
import RouteParamsEditor from '@/components/RouteParamsEditor.vue'

// 使用菜单管理store
const menuManagementStore = useMenuManagementStore()

// 表格引用
const menuTableRef = ref()
const menuFormRef = ref()
const routeParamsEditorRef = ref()

// 从store中解构响应式状态
const {
  menuList,
  loading,
  submitLoading,
  selectedMenus,
  isExpandAll,
  searchForm,
  menuForm,
  dialogVisible,
  dialogTitle,
  isEdit
} = storeToRefs(menuManagementStore)

// 从store中解构方法
const {
  fetchMenuList,
  fetchMenuById,
  createMenu,
  updateMenu,
  deleteMenu,
  batchUpdateStatus,
  updateMenuStatus,
  setSelectedMenus,
  setExpandAll,
  openDialog,
  closeDialog,
  resetMenuForm
} = menuManagementStore

// 表单验证规则
const menuFormRules = computed(() => {
  const rules = {
    menuName: [
      { required: true, message: '请输入菜单名称', trigger: 'blur' },
      { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
    ],
    menuType: [
      { required: true, message: '请选择菜单类型', trigger: 'change' }
    ],
    menuCode: [
      { required: true, message: '请输入菜单编码', trigger: 'blur' },
      { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
    ]
  }

  // 目录和菜单需要路由路径
  if (menuForm.value.menuType !== 3) {
    rules.routePath = [
      { required: true, message: '请输入路由路径', trigger: 'blur' },
      {
        validator: (_, value, callback) => {
          if (menuForm.value.externalLink === 1) {
            // 外部链接验证
            if (!value || (!value.startsWith('http://') && !value.startsWith('https://'))) {
              callback(new Error('外部链接必须以 http:// 或 https:// 开头'))
            } else {
              callback()
            }
          } else {
            // 内部路由验证
            if (!value || !value.startsWith('/')) {
              callback(new Error('路由路径必须以 / 开头'))
            } else {
              callback()
            }
          }
        },
        trigger: 'blur'
      }
    ]
  }

  // 菜单类型需要组件路径（非外部链接）
  if (menuForm.value.menuType === 2 && menuForm.value.externalLink === 0) {
    rules.componentPath = [
      { required: true, message: '请选择或输入组件路径', trigger: 'blur' }
    ]
  }

  return rules
})

// 计算属性
const hasSelection = computed(() => selectedMenus.value && selectedMenus.value.length > 0)

// 从现有菜单数据中过滤出可作为父菜单的选项
const parentMenuOptions = computed(() => {
  const buildParentOptions = (menus, currentEditId = null) => {
    const options = []

    menus.forEach(menu => {
      // 排除按钮类型(menuType=3)，因为按钮不能作为父菜单
      // 如果是编辑模式，排除当前正在编辑的菜单（避免循环引用）
      if (menu.menuType !== 3 && menu.id !== currentEditId) {
        const option = {
          id: menu.id,
          menuName: menu.menuName,
          menuType: menu.menuType,
          children: []
        }

        // 递归处理子菜单
        if (menu.children && menu.children.length > 0) {
          option.children = buildParentOptions(menu.children, currentEditId)
        }

        options.push(option)
      }
    })

    return options
  }

  // 如果是编辑模式，传入当前编辑的菜单ID以避免循环引用
  const currentEditId = isEdit.value ? menuForm.id : null
  return buildParentOptions(menuList.value || [], currentEditId)
})

// 方法
const handleSearch = () => {
  fetchMenuList()
}

const handleReset = () => {
  menuManagementStore.resetSearchForm()
  fetchMenuList()
}

// 计算下一个排序号
const getNextSortOrder = (parentId = null) => {
  const allMenus = menuManagementStore.getFlatMenus()

  // 过滤出同级菜单
  const siblingMenus = allMenus.filter(menu => {
    if (parentId === null) {
      // 顶级菜单：parentId为null或undefined
      return !menu.parentId
    } else {
      // 子菜单：parentId相同
      return menu.parentId === parentId
    }
  })

  // 如果没有同级菜单，从1开始
  if (siblingMenus.length === 0) {
    return 1
  }

  // 找到最大的排序号，然后+1
  const maxSortOrder = Math.max(...siblingMenus.map(menu => menu.sortOrder || 0))
  return maxSortOrder + 1
}

// 更新排序号（当父级菜单改变时）
const updateSortOrderForParent = (newParentId) => {
  // 只在新增模式下自动更新排序号，编辑模式保持原有排序号
  if (!isEdit.value) {
    const newSortOrder = getNextSortOrder(newParentId)
    const oldSortOrder = menuForm.value.sortOrder

    // 只有排序号真正改变时才更新
    if (newSortOrder !== oldSortOrder) {
      menuForm.value.sortOrder = newSortOrder
    }
  }
}

const handleAdd = () => {
  resetMenuForm()
  // 设置自动计算的排序号（顶级菜单）
  menuForm.value.sortOrder = getNextSortOrder(null)
  openDialog('新增菜单', false)
}

const handleAddChild = (row) => {
  resetMenuForm()
  // 重置后再设置父菜单ID，确保不被重置覆盖
  menuForm.value.parentId = row.id
  // 设置自动计算的排序号（子菜单）
  menuForm.value.sortOrder = getNextSortOrder(row.id)
  openDialog('新增子菜单', false)
}

const handleEdit = async (row) => {
  try {
    const response = await fetchMenuById(row.id)
    menuManagementStore.setMenuForm(response)
    openDialog('编辑菜单', true)
  } catch (error) {
    // 错误已在store中处理
  }
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除菜单"${row.menuName}"吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteMenu(row.id)
  } catch (error) {
    if (error !== 'cancel') {
      // 错误已在store中处理
    }
  }
}

const handleStatusChange = async (row) => {
  try {
    await updateMenuStatus(row.id, row.status)
  } catch (error) {
    // 恢复原状态
    row.status = row.status === 1 ? 0 : 1
  }
}

const handleBatchEnable = async () => {
  try {
    const menuIds = selectedMenus.value.map(menu => menu.id)
    await batchUpdateStatus(menuIds, 1)
  } catch (error) {
    // 错误已在store中处理
  }
}

const handleBatchDisable = async () => {
  try {
    const menuIds = selectedMenus.value.map(menu => menu.id)
    await batchUpdateStatus(menuIds, 0)
  } catch (error) {
    // 错误已在store中处理
  }
}

const handleExpandAll = () => {
  setExpandAll(!isExpandAll.value)

  if (menuTableRef.value && menuList.value) {
    if (isExpandAll.value) {
      // 展开所有行
      const expandAllRows = (data) => {
        data.forEach(row => {
          if (row.children && row.children.length > 0) {
            menuTableRef.value.toggleRowExpansion(row, true)
            expandAllRows(row.children)
          }
        })
      }
      expandAllRows(menuList.value)
    } else {
      // 收起所有行
      const collapseAllRows = (data) => {
        data.forEach(row => {
          if (row.children && row.children.length > 0) {
            menuTableRef.value.toggleRowExpansion(row, false)
            collapseAllRows(row.children)
          }
        })
      }
      collapseAllRows(menuList.value)
    }
  }
}

const handleSelectionChange = (selection) => {
  setSelectedMenus(selection)
}

// 生成随机菜单编码
const generateMenuCode = () => {
  const timestamp = Date.now()
  const randomSuffix = Math.random().toString(36).substring(2, 6).toUpperCase()
  const menuCode = `MENU_${timestamp}_${randomSuffix}`
  menuForm.value.menuCode = menuCode

  // 提示用户已生成
  ElMessage.success('菜单编码已生成')
}

// 监听父级菜单变化，自动更新排序号
watch(
  () => menuForm.value.parentId,
  (newParentId, oldParentId) => {
    // 只有在值真正改变时才更新，避免初始化时触发
    if (newParentId !== oldParentId && dialogVisible.value) {
      updateSortOrderForParent(newParentId)
    }
  }
)

const handleSubmit = async () => {
  try {
    await menuFormRef.value.validate()

    // 验证路由参数格式
    if (menuForm.value.routeParams && menuForm.value.routeParams.trim()) {
      if (routeParamsEditorRef.value) {
        const isJsonValid = routeParamsEditorRef.value.isValid()
        if (!isJsonValid) {
          ElMessage.error('路由参数JSON格式不正确，请检查后重试')
          return
        }
      } else {
        // 如果没有编辑器引用，直接验证JSON格式
        try {
          JSON.parse(menuForm.value.routeParams)
        } catch (error) {
          ElMessage.error('路由参数JSON格式错误，请输入有效的JSON格式')
          return
        }
      }
    }

    if (isEdit.value) {
      await updateMenu(menuForm.value)
    } else {
      await createMenu(menuForm.value)
    }

    closeDialog()
  } catch (error) {
    // 错误已在store中处理
  }
}

const getMenuTypeText = (type) => {
  const typeMap = {
    1: '目录',
    2: '菜单',
    3: '按钮'
  }
  return typeMap[type] || '未知'
}

const getMenuTypeTagType = (type) => {
  const typeMap = {
    1: 'info',
    2: 'success',
    3: 'warning'
  }
  return typeMap[type] || 'info'
}

// 生命周期
onMounted(() => {
  fetchMenuList()
})
</script>

<style lang="scss" scoped>
/* 使用全局统一的管理页面样式 */

// 菜单管理特有的搜索区域样式（如果有特殊需求可以在这里添加）

// 菜单名称单元格样式
.menu-name-cell {
  display: flex;
  align-items: center;
  gap: 12px;
  min-height: 40px;

  .menu-icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 6px;
    background: #f8fafc;
    flex-shrink: 0;

    .menu-icon {
      width: 16px;
      height: 16px;
      color: var(--primary-color);

      &.default-icon {
        color: var(--text-color-placeholder);
      }
    }
  }

  .menu-name {
    font-weight: 500;
    color: var(--text-color-primary);
    line-height: 1.5;
  }
}

// 路径和组件样式
.route-path {
  color: var(--primary-color);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  background: var(--bg-color-page);
  padding: 2px 6px;
  border-radius: 4px;
}

.component-path {
  color: var(--success-color);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
}

.text-gray {
  color: var(--text-color-placeholder);
}

// 菜单管理特有的表格样式（如果有特殊需求可以在这里添加）


// 表单标签提示图标样式
.form-label-tip {
  margin-right: 6px;
  color: var(--text-color-placeholder);
  font-size: 14px;
  cursor: help;
  transition: color 0.3s ease;
  vertical-align: middle;

  &:hover {
    color: var(--primary-color);
  }
}

// 生成编码按钮样式
.generate-code-btn {
  padding: 4px 8px;
  height: auto;
  border: none;
  color: var(--primary-color);

  &:hover {
    background: var(--primary-lightest);
    color: var(--primary-dark);
  }

  .el-icon {
    font-size: 14px;
  }
}

// 对话框表单标签对齐（只应用于对话框中的表单）
:deep(.el-dialog) {
  .el-form-item__label {
    display: flex !important;
    align-items: center !important;
    justify-content: flex-end !important;
    height: auto !important;
    line-height: normal !important;

    .form-label-tip {
      margin-right: 6px;
      flex-shrink: 0;
    }

    span {
      flex-shrink: 0;
    }
  }
}

// 表单项提示样式（保留用于其他地方）
.form-item-tip {
  font-size: 12px;
  color: var(--text-color-placeholder);
  margin-top: 4px;
  line-height: 1.4;
}

// 父菜单选择器样式
.tree-select-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .menu-type-tag {
    margin-left: 8px;
  }
}

:deep(.el-tree-select) {
  .el-tree-node__content {
    height: auto;
    padding: 8px 0;
  }
}

/* 搜索表单控件样式 */
.search-input {
  width: 200px;
}

.search-select {
  width: 150px;
}

// 页面特定样式 - 移动端适配已提取到公共样式文件
@media (max-width: 768px) {
  .management-page {
    padding: 12px;
  }

  .table-section {
    overflow-x: auto;
  }
}
</style>
