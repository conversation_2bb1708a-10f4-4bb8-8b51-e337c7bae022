<template>
  <el-dialog
    v-model="dialogVisible"
    title="清理操作日志"
    :width="dialogWidth"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
    destroy-on-close
    class="cleanup-dialog"
    @close="handleClose"
  >
    <div class="cleanup-content">
      <el-form 
        :model="cleanupForm" 
        :rules="rules" 
        ref="cleanupFormRef" 
        :label-width="labelWidth"
        :label-position="labelPosition"
        @submit.prevent
      >
        <el-form-item label="清理天数" prop="days">
          <el-input-number
            v-model="cleanupForm.days"
            :min="1"
            :max="365"
            :step="1"
            :style="{ width: isMobile ? '100%' : '200px' }"
          />
          <div class="form-tip">
            清理 {{ cleanupForm.days }} 天前的操作日志
          </div>
        </el-form-item>

        <el-form-item label="确认清理">
          <el-checkbox v-model="confirmCleanup" class="cleanup-confirm-checkbox">
            我确认要清理 {{ cleanupForm.days }} 天前的操作日志，此操作不可恢复
          </el-checkbox>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="warning"
          @click="handleClearAll"
          :loading="clearAllLoading"
        >
          清空所有日志
        </el-button>
        <el-button
          type="danger"
          @click="handleCleanup"
          :loading="cleanupLoading"
          :disabled="!confirmCleanup"
        >
          确认清理
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { cleanupExpiredOperationLogs, clearAllOperationLogs } from '@/api/operationLog'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const cleanupLoading = ref(false)
const clearAllLoading = ref(false)
const confirmCleanup = ref(false)
const cleanupFormRef = ref()
const isMobile = ref(false)

// 表单数据
const cleanupForm = reactive({
  days: 30
})

// 表单验证规则
const rules = {
  days: [
    { required: true, message: '请输入清理天数', trigger: 'blur' },
    { type: 'number', min: 1, max: 365, message: '清理天数必须在1-365之间', trigger: 'blur' }
  ]
}

// 检查是否为移动端
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const dialogWidth = computed(() => {
  return isMobile.value ? '95%' : '500px'
})

const labelWidth = computed(() => {
  return isMobile.value ? '80px' : '120px'
})

const labelPosition = computed(() => {
  return isMobile.value ? 'top' : 'right'
})

// 方法
const handleClose = () => {
  resetForm()
  dialogVisible.value = false
}

const resetForm = () => {
  cleanupForm.days = 30
  confirmCleanup.value = false
  if (cleanupFormRef.value) {
    cleanupFormRef.value.clearValidate()
  }
}

// 清理过期日志
const handleCleanup = async () => {
  try {
    await cleanupFormRef.value.validate()
    
    await ElMessageBox.confirm(
      `确定要清理 ${cleanupForm.days} 天前的操作日志吗？此操作不可恢复！`,
      '清理确认',
      {
        confirmButtonText: '确定清理',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    cleanupLoading.value = true
    
    const response = await cleanupExpiredOperationLogs({
      days: cleanupForm.days
    })

    
    ElMessage.success(`清理完成，共删除 ${response ? response.data : 0} 条记录`)
    emit('success')
    handleClose()
  } catch (error) {
    if (error !== 'cancel') {
      // 错误提示由响应拦截器处理，这里只需要记录日志
      console.error('清理失败:', error)
    }
  } finally {
    cleanupLoading.value = false
  }
}

// 清空所有日志
const handleClearAll = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有操作日志吗？此操作将删除所有历史记录，不可恢复！',
      '清空确认',
      {
        confirmButtonText: '确定清空',
        cancelButtonText: '取消',
        type: 'error',
        dangerouslyUseHTMLString: true
      }
    )

    clearAllLoading.value = true
    
    const response = await clearAllOperationLogs()

    ElMessage.success(`清空完成，共删除 ${response || 0} 条记录`)
    emit('success')
    handleClose()
  } catch (error) {
    if (error !== 'cancel') {
      // 错误提示由响应拦截器处理，这里只需要记录日志
      console.error('清空失败:', error)
    }
  } finally {
    clearAllLoading.value = false
  }
}

// 生命周期
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<style scoped>
.cleanup-content {
  padding: 20px 0;
}

.form-tip {
  margin-top: 8px;
  font-size: 12px;
  color: var(--text-color-secondary);
  line-height: 1.4;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 确认复选框文本换行 */
.cleanup-confirm-checkbox :deep(.el-checkbox__label) {
  white-space: normal;
  word-wrap: break-word;
  line-height: 1.5;
  max-width: 350px;
  display: inline-block;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .cleanup-dialog :deep(.el-dialog) {
    width: 95% !important;
    margin: 5vh auto !important;
  }
  
  .cleanup-dialog :deep(.el-dialog__body) {
    padding: 16px;
  }
  
  .cleanup-content {
    padding: 0;
  }
  
  .cleanup-dialog :deep(.el-form-item__label) {
    font-size: 14px;
    margin-bottom: 4px;
  }
  
  .cleanup-dialog :deep(.el-input-number) {
    width: 100%;
  }
  
  .cleanup-confirm-checkbox :deep(.el-checkbox__label) {
    white-space: normal !important;
    word-wrap: break-word !important;
    word-break: break-all !important;
    max-width: calc(100vw - 120px) !important;
    display: inline-block !important;
  }
  
  .dialog-footer {
    flex-direction: column-reverse;
    gap: 8px;
    padding: 16px;
  }

  .dialog-footer .el-button {
    width: 100%;
    margin: 0;
    height: 40px;
  }
  
  .form-tip {
    font-size: 12px;
    margin-top: 8px;
    line-height: 1.4;
  }
}
</style>
