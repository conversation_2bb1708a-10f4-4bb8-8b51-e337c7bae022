<template>
  <el-dialog
    v-model="dialogVisible"
    title="清理过期日志"
    :width="dialogWidth"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
    destroy-on-close
    class="cleanup-dialog"
    @close="handleClose"
  >
    <div class="cleanup-content">
      <el-alert
        title="注意"
        type="warning"
        description="清理操作不可恢复，请谨慎操作！建议在清理前先导出重要的日志数据。"
        show-icon
        :closable="false"
        style="margin-bottom: 20px"
      />

      <el-form
        :model="cleanupForm"
        :rules="rules"
        ref="cleanupFormRef"
        :label-width="labelWidth"
        :label-position="labelPosition"
        @submit.prevent
      >
        <el-form-item label="清理天数" prop="days">
          <el-input-number
            v-model="cleanupForm.days"
            :min="1"
            :max="365"
            :step="1"
            style="width: 200px"
          />
          <div class="form-tip">
            清理 {{ cleanupForm.days }} 天前的登录日志
          </div>
        </el-form-item>



        <el-form-item label="确认清理">
          <el-checkbox v-model="confirmCleanup" class="cleanup-confirm-checkbox">
            我确认要清理 {{ cleanupForm.days }} 天前的登录日志，此操作不可恢复
          </el-checkbox>
        </el-form-item>
      </el-form>


    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="warning"
          @click="handleClearAll"
          :loading="clearAllLoading"
        >
          清空所有日志
        </el-button>
        <el-button
          type="danger"
          @click="handleCleanup"
          :loading="cleanupLoading"
          :disabled="!confirmCleanup"
        >
          确认清理
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { cleanupExpiredLogs, clearAllLogs } from '@/api/loginLog'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const cleanupLoading = ref(false)
const clearAllLoading = ref(false)
const confirmCleanup = ref(false)
const cleanupFormRef = ref()
const isMobile = ref(false)

// 表单数据
const cleanupForm = reactive({
  days: 30
})

// 表单验证规则
const rules = {
  days: [
    { required: true, message: '请输入清理天数', trigger: 'blur' },
    { type: 'number', min: 1, max: 365, message: '清理天数必须在1-365之间', trigger: 'blur' }
  ]
}

// 检查是否为移动端
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const dialogWidth = computed(() => {
  return isMobile.value ? '95%' : '500px'
})

const labelWidth = computed(() => {
  return isMobile.value ? '80px' : '120px'
})

const labelPosition = computed(() => {
  return isMobile.value ? 'top' : 'right'
})

// 方法
const handleClose = () => {
  resetForm()
  dialogVisible.value = false
}

const resetForm = () => {
  cleanupForm.days = 30
  confirmCleanup.value = false
  if (cleanupFormRef.value) {
    cleanupFormRef.value.clearValidate()
  }
}



const handleCleanup = async () => {
  try {
    await cleanupFormRef.value.validate()
    
    if (!confirmCleanup.value) {
      ElMessage.warning('请先确认清理操作')
      return
    }

    await ElMessageBox.confirm(
      `确定要清理 ${cleanupForm.days} 天前的登录日志吗？此操作不可恢复！`,
      '清理确认',
      {
        confirmButtonText: '确定清理',
        cancelButtonText: '取消',
        type: 'error'
      }
    )

    cleanupLoading.value = true
    
    const params = {
      days: cleanupForm.days
    }
    
    await cleanupExpiredLogs(params)
    
    ElMessage.success('清理完成')
    emit('success')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清理失败:', error)
    }
  } finally {
    cleanupLoading.value = false
  }
}

// 清空所有日志
const handleClearAll = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有登录日志吗？此操作将删除所有历史记录，不可恢复！',
      '清空确认',
      {
        confirmButtonText: '确定清空',
        cancelButtonText: '取消',
        type: 'error',
        dangerouslyUseHTMLString: true
      }
    )

    clearAllLoading.value = true

    const response = await clearAllLogs()

    ElMessage.success(`清空完成，共删除 ${response.data || 0} 条记录`)
    emit('success')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清空失败:', error)
      ElMessage.error('清空失败')
    }
  } finally {
    clearAllLoading.value = false
  }
}

// 生命周期
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<style scoped>
.cleanup-content {
  padding: 0 4px;
}

.form-tip {
  font-size: 12px;
  color: var(--text-color-secondary);
  margin-top: 4px;
}



.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 确认复选框文本换行 */
.cleanup-confirm-checkbox :deep(.el-checkbox__label) {
  white-space: normal;
  word-wrap: break-word;
  line-height: 1.5;
  max-width: 350px;
  display: inline-block;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .cleanup-dialog :deep(.el-dialog) {
    width: 95% !important;
    margin: 5vh auto !important;
  }

  .cleanup-dialog :deep(.el-dialog__body) {
    padding: 16px;
  }

  .cleanup-dialog :deep(.el-form-item__label) {
    font-size: 14px;
    margin-bottom: 4px;
  }

  .cleanup-dialog :deep(.el-input-number) {
    width: 100%;
  }

  .cleanup-dialog :deep(.el-checkbox__label) {
    font-size: 14px;
    line-height: 1.4;
  }

  .cleanup-confirm-checkbox :deep(.el-checkbox__label) {
    white-space: normal !important;
    word-wrap: break-word !important;
    word-break: break-all !important;
    max-width: calc(100vw - 120px) !important;
    display: inline-block !important;
  }

  .dialog-footer {
    flex-direction: column-reverse;
    gap: 8px;
    padding: 16px;
  }

  .dialog-footer .el-button {
    width: 100%;
    margin: 0;
    height: 40px;
  }
}
</style>
