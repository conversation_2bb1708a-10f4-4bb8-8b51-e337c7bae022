<template>
  <el-dialog
    v-model="dialogVisible"
    title="操作日志详情"
    :width="dialogWidth"
    :fullscreen="isMobile"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    destroy-on-close
    append-to-body
    class="operation-log-detail-dialog"
  >
    <div v-loading="loading" class="detail-content">
      <el-descriptions v-if="logDetail" :column="isMobile ? 1 : 2" border>
        <el-descriptions-item label="操作ID">
          {{ logDetail.id }}
        </el-descriptions-item>
        <el-descriptions-item label="用户名">
          {{ logDetail.username }}
        </el-descriptions-item>
        <el-descriptions-item label="操作时间">
          {{ logDetail.operationTime }}
        </el-descriptions-item>
        <el-descriptions-item label="操作模块">
          {{ logDetail.operationModule }}
        </el-descriptions-item>
        <el-descriptions-item label="操作类型">
          <el-tag :type="getOperationTypeTagType(logDetail.operationType)">
            {{ OPERATION_TYPE_LABELS[logDetail.operationType] || logDetail.operationType }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="操作状态">
          <el-tag :type="logDetail.operationStatus === 1 ? 'success' : 'danger'">
            {{ OPERATION_STATUS_LABELS[logDetail.operationStatus] }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="操作描述" :span="isMobile ? 1 : 2">
          {{ logDetail.operationDesc }}
        </el-descriptions-item>
        <el-descriptions-item v-if="logDetail.errorMessage" label="错误信息" :span="isMobile ? 1 : 2">
          <el-text type="danger">{{ logDetail.errorMessage }}</el-text>
        </el-descriptions-item>
        <el-descriptions-item label="请求方法">
          <el-tag>{{ logDetail.requestMethod }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="执行时间">
          {{ logDetail.executionTime }}ms
        </el-descriptions-item>
        <el-descriptions-item label="请求URL" :span="isMobile ? 1 : 2">
          <el-text class="url-text">{{ logDetail.requestUrl }}</el-text>
        </el-descriptions-item>
        <el-descriptions-item label="客户端IP">
          {{ logDetail.clientIp }}
        </el-descriptions-item>
        <el-descriptions-item v-if="logDetail.location" label="地理位置">
          {{ logDetail.location }}
        </el-descriptions-item>
        <el-descriptions-item label="响应状态">
          <el-tag :type="logDetail.responseStatus >= 200 && logDetail.responseStatus < 300 ? 'success' : 'danger'">
            {{ logDetail.responseStatus }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item v-if="logDetail.responseMessage" label="响应消息" :span="isMobile ? 1 : 2">
          {{ logDetail.responseMessage }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ logDetail.createTime }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 请求参数 -->
      <div v-if="logDetail && logDetail.requestParams" class="detail-section">
        <h4>请求参数</h4>
        <el-input
          v-model="formattedRequestParams"
          type="textarea"
          :rows="6"
          readonly
          class="json-textarea"
        />
      </div>

      <!-- 响应结果 -->
      <div v-if="logDetail && logDetail.responseResult" class="detail-section">
        <h4>响应结果</h4>
        <el-input
          v-model="formattedResponseResult"
          type="textarea"
          :rows="6"
          readonly
          class="json-textarea"
        />
      </div>

      <!-- User Agent -->
      <div v-if="logDetail && logDetail.userAgent" class="detail-section">
        <h4>UserAgent</h4>
        <el-input
          v-model="logDetail.userAgent"
          type="textarea"
          :rows="3"
          readonly
          class="user-agent-textarea"
        />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getOperationLogDetail, OPERATION_TYPE_LABELS, OPERATION_STATUS_LABELS } from '@/api/operationLog'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  logId: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const loading = ref(false)
const logDetail = ref(null)
const isMobile = ref(false)

// 检查是否为移动端
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const dialogWidth = computed(() => {
  if (isMobile.value) {
    return '95%'
  } else if (window.innerWidth <= 1024) {
    return '90%'
  } else {
    return '800px'
  }
})

const formattedRequestParams = computed(() => {
  if (!logDetail.value?.requestParams) return ''
  try {
    const params = typeof logDetail.value.requestParams === 'string' 
      ? JSON.parse(logDetail.value.requestParams)
      : logDetail.value.requestParams
    return JSON.stringify(params, null, 2)
  } catch (error) {
    return logDetail.value.requestParams
  }
})

const formattedResponseResult = computed(() => {
  if (!logDetail.value?.responseResult) return ''
  try {
    const result = typeof logDetail.value.responseResult === 'string'
      ? JSON.parse(logDetail.value.responseResult)
      : logDetail.value.responseResult
    return JSON.stringify(result, null, 2)
  } catch (error) {
    return logDetail.value.responseResult
  }
})

// 获取操作类型标签类型
const getOperationTypeTagType = (type) => {
  const typeMap = {
    CREATE: 'success',
    UPDATE: 'warning',
    DELETE: 'danger',
    QUERY: 'info',
    LOGIN: 'success',
    LOGOUT: 'info',
    EXPORT: 'warning',
    IMPORT: 'warning',
    APPROVE: 'success',
    REJECT: 'danger',
    ENABLE: 'success',
    DISABLE: 'warning',
    RESET: 'warning',
    OTHER: 'info'
  }
  return typeMap[type] || 'info'
}

// 方法
const loadLogDetail = async () => {
  if (!props.logId) return

  try {
    loading.value = true
    const response = await getOperationLogDetail(props.logId)

    logDetail.value = response
  } catch (error) {
    // 错误提示由响应拦截器处理，这里只需要记录日志
    console.error('加载操作日志详情失败:', error)
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  logDetail.value = null
  dialogVisible.value = false
}

// 监听器
watch(() => props.visible, (newValue) => {
  if (newValue && props.logId) {
    loadLogDetail()
  }
})

// 生命周期
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<style scoped>
.detail-content {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-section {
  margin-top: 20px;
}

.detail-section h4 {
  margin-bottom: 10px;
  color: var(--text-color-primary);
  font-weight: 600;
}

.json-textarea :deep(.el-textarea__inner) {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
}

.user-agent-textarea :deep(.el-textarea__inner) {
  font-size: 12px;
  line-height: 1.4;
}

.url-text {
  word-break: break-all;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .operation-log-detail-dialog :deep(.el-dialog) {
    width: 95% !important;
    margin: 5vh auto !important;
  }
  
  .operation-log-detail-dialog :deep(.el-dialog__body) {
    padding: 16px;
  }
  
  .detail-content {
    max-height: 60vh;
  }
  
  .dialog-footer {
    padding: 16px;
  }

  .dialog-footer .el-button {
    width: 100%;
    margin: 0;
    height: 40px;
  }
}
</style>
