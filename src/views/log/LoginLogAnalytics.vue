<template>
  <div class="analytics-page">
   

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="16">
        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <div class="stat-card">
            <div class="stat-icon success">
              <el-icon><Check /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ statsData.successLogins || 0 }}</div>
              <div class="stat-label">成功登录</div>
            </div>
          </div>
        </el-col>
        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <div class="stat-card">
            <div class="stat-icon danger">
              <el-icon><Close /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ statsData.failureLogins || 0 }}</div>
              <div class="stat-label">失败登录</div>
            </div>
          </div>
        </el-col>
        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <div class="stat-card">
            <div class="stat-icon warning">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ statsData.suspiciousLogins || 0 }}</div>
              <div class="stat-label">可疑登录</div>
            </div>
          </div>
        </el-col>
        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <div class="stat-card">
            <div class="stat-icon info">
              <el-icon><User /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ statsData.uniqueUsers || 0 }}</div>
              <div class="stat-label">活跃用户</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <!-- 登录趋势图表 -->
      <div class="chart-container">
        <div class="chart-header">
          <h3>登录趋势分析</h3>
          <p>最近7天的登录趋势变化</p>
        </div>
        <div 
          ref="trendChartRef" 
          class="chart" 
          v-loading="loading.trend"
        ></div>
      </div>

      <!-- 图表网格 -->
      <el-row :gutter="16" class="chart-grid">
        <!-- 热门IP图表 -->
        <el-col :xs="24" :sm="24" :md="12" :lg="12">
          <div class="chart-container">
            <div class="chart-header">
              <h3>热门IP地址</h3>
              <p>登录次数最多的IP地址</p>
            </div>
            <div 
              ref="topIpsChartRef" 
              class="chart small-chart"
              v-loading="loading.topIps"
            ></div>
          </div>
        </el-col>

        <!-- 设备统计图表 -->
        <el-col :xs="24" :sm="24" :md="12" :lg="12">
          <div class="chart-container">
            <div class="chart-header">
              <h3>设备类型分布</h3>
              <p>不同设备类型的登录占比</p>
            </div>
            <div 
              ref="deviceChartRef" 
              class="chart small-chart"
              v-loading="loading.deviceStats"
            ></div>
          </div>
        </el-col>
      </el-row>

      <!-- 地理位置图表 -->
      <div class="chart-container">
        <div class="chart-header">
          <h3>地理位置分布</h3>
          <p>不同地区的登录分布情况</p>
        </div>
        <div 
          ref="locationChartRef" 
          class="chart"
          v-loading="loading.locationStats"
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Check, Close, Warning, User
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import {
  getLoginStatistics,
  getLoginTrend,
  getTopLoginIps,
  getDeviceStats,
  getLocationStats
} from '@/api/loginLog'

// 响应式数据
const loading = reactive({
  trend: false,
  topIps: false,
  deviceStats: false,
  locationStats: false
})

const statsData = reactive({
  successLogins: 0,
  failureLogins: 0,
  suspiciousLogins: 0,
  uniqueUsers: 0
})

// 图表引用
const trendChartRef = ref()
const topIpsChartRef = ref()
const deviceChartRef = ref()
const locationChartRef = ref()

// 图表实例
let trendChart = null
let topIpsChart = null
let deviceChart = null
let locationChart = null

// 方法

// 加载统计数据
const loadStatsData = async () => {
  try {
    const response = await getLoginStatistics()
    console.log('统计数据API响应:', response)

    // 按照接口文档使用真实数据结构
    const data = response.data || response
    console.log('解析后的统计数据:', data)

    Object.assign(statsData, {
      successLogins: data.successLogins || 0,
      failureLogins: data.failureLogins || 0,
      suspiciousLogins: data.suspiciousLogins || 0,
      uniqueUsers: data.uniqueUsers || 0
    })

    console.log('最终统计数据:', statsData)
  } catch (error) {
    console.error('加载统计数据失败:', error)
    ElMessage.error('加载统计数据失败')
    // API失败时使用默认值
    Object.assign(statsData, {
      successLogins: 0,
      failureLogins: 0,
      suspiciousLogins: 0,
      uniqueUsers: 0
    })
  }
}

// 加载登录趋势图表
const loadTrendChart = async () => {
  loading.trend = true
  try {
    const response = await getLoginTrend({ days: 7 })
    console.log('登录趋势API响应:', response)

    if (trendChart) {
      trendChart.dispose()
    }

    trendChart = echarts.init(trendChartRef.value)

    // 按照接口文档使用真实数据
    const data = response.data || response
    const dailyStats = data.dailyStats || []
    console.log('趋势数据dailyStats:', dailyStats)
    const dates = []
    const successData = []
    const failureData = []

    if (dailyStats.length > 0) {
      // 使用API返回的真实数据
      dailyStats.forEach(item => {
        dates.push(item.date)
        successData.push(item.success_count || 0)
        failureData.push(item.failure_count || 0)
      })
    } else {
      // 如果没有数据，生成默认的7天空数据
      for (let i = 6; i >= 0; i--) {
        const date = new Date()
        date.setDate(date.getDate() - i)
        dates.push(date.toLocaleDateString())
        successData.push(0)
        failureData.push(0)
      }
    }
    
    const option = {
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['成功登录', '失败登录']
      },
      xAxis: {
        type: 'category',
        data: dates
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '成功登录',
          type: 'line',
          data: successData,
          smooth: true,
          itemStyle: { color: '#67C23A' }
        },
        {
          name: '失败登录',
          type: 'line',
          data: failureData,
          smooth: true,
          itemStyle: { color: '#F56C6C' }
        }
      ]
    }
    
    trendChart.setOption(option)
  } catch (error) {
    console.error('加载登录趋势失败:', error)
  } finally {
    loading.trend = false
  }
}

// 加载热门IP图表
const loadTopIpsChart = async () => {
  loading.topIps = true
  try {
    const response = await getTopLoginIps({ limit: 10, days: 7 })

    if (topIpsChart) {
      topIpsChart.dispose()
    }

    topIpsChart = echarts.init(topIpsChartRef.value)

    // 按照接口文档使用真实数据
    const data = response.data || response || []
    const ipData = data.map(item => ({
      ip: item.client_ip,
      count: item.total_count,
      location: item.location || '未知'
    }))

    // 如果没有数据，显示空状态
    if (ipData.length === 0) {
      ipData.push({ ip: '暂无数据', count: 0, location: '' })
    }

    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'shadow' },
        formatter: function(params) {
          const dataIndex = params[0].dataIndex
          const item = ipData[dataIndex]
          return `IP地址: ${item.ip}<br/>登录次数: ${item.count}<br/>地理位置: ${item.location || '未知'}`
        }
      },
      xAxis: {
        type: 'value'
      },
      yAxis: {
        type: 'category',
        data: ipData.map(item => item.ip)
      },
      series: [{
        name: '登录次数',
        type: 'bar',
        data: ipData.map(item => item.count),
        itemStyle: { color: '#409EFF' }
      }]
    }

    topIpsChart.setOption(option)
  } catch (error) {
    console.error('加载热门IP失败:', error)
  } finally {
    loading.topIps = false
  }
}

// 加载设备统计图表
const loadDeviceChart = async () => {
  loading.deviceStats = true
  try {
    const response = await getDeviceStats({ days: 7 })

    if (deviceChart) {
      deviceChart.dispose()
    }

    deviceChart = echarts.init(deviceChartRef.value)

    // 按照接口文档使用真实数据
    const data = response.data || response
    const deviceTypeStats = data.deviceTypeStats || []
    const deviceData = deviceTypeStats.map(item => ({
      name: item.device_type === 'DESKTOP' ? 'PC' :
            item.device_type === 'MOBILE' ? 'Mobile' :
            item.device_type === 'TABLET' ? 'Tablet' :
            item.device_type || 'Other',
      value: item.count || 0
    }))

    // 如果没有数据，显示空状态
    if (deviceData.length === 0) {
      deviceData.push({ name: '暂无数据', value: 1 })
    }

    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left'
      },
      series: [{
        name: '设备类型',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['60%', '50%'],
        data: deviceData,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        }
      }]
    }

    deviceChart.setOption(option)
  } catch (error) {
    console.error('加载设备统计失败:', error)
  } finally {
    loading.deviceStats = false
  }
}

// 加载地理位置统计图表
const loadLocationChart = async () => {
  loading.locationStats = true
  try {
    const response = await getLocationStats({ days: 7 })

    if (locationChart) {
      locationChart.dispose()
    }

    locationChart = echarts.init(locationChartRef.value)

    // 按照接口文档使用真实数据
    const data = response.data || response
    const locationStats = data.locationStats || []
    const locationData = locationStats.map(item => ({
      name: item.location || '未知',
      value: item.count || 0,
      fullLocation: item.location || '未知'
    }))

    // 如果没有数据，显示空状态
    if (locationData.length === 0) {
      locationData.push({ name: '暂无数据', value: 0, fullLocation: '' })
    }

    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'shadow' },
        formatter: function(params) {
          const dataIndex = params[0].dataIndex
          const item = locationData[dataIndex]
          return `地区: ${item.fullLocation || item.name}<br/>登录次数: ${item.value}`
        }
      },
      xAxis: {
        type: 'category',
        data: locationData.map(item => item.name),
        axisLabel: { rotate: 45 }
      },
      yAxis: {
        type: 'value',
        name: '登录次数'
      },
      series: [{
        name: '登录次数',
        type: 'bar',
        data: locationData.map(item => item.value),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#188df0' }
          ])
        }
      }]
    }

    locationChart.setOption(option)
  } catch (error) {
    console.error('加载地理位置统计失败:', error)
  } finally {
    loading.locationStats = false
  }
}

// 生命周期
onMounted(async () => {
  await nextTick()

  // 初始化所有数据和图表
  await Promise.all([
    loadStatsData(),
    loadTrendChart()
  ])

  // 延迟加载其他图表，避免同时渲染造成卡顿
  setTimeout(async () => {
    await Promise.all([
      loadTopIpsChart(),
      loadDeviceChart(),
      loadLocationChart()
    ])
  }, 500)

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)

  // 销毁图表实例
  if (trendChart) trendChart.dispose()
  if (topIpsChart) topIpsChart.dispose()
  if (deviceChart) deviceChart.dispose()
  if (locationChart) locationChart.dispose()
})

// 窗口大小变化处理
const handleResize = () => {
  setTimeout(() => {
    if (trendChart) trendChart.resize()
    if (topIpsChart) topIpsChart.resize()
    if (deviceChart) deviceChart.resize()
    if (locationChart) locationChart.resize()
  }, 100)
}
</script>

<style scoped>
.analytics-page {
  padding: 16px;
  background: var(--bg-color-page);
  min-height: 100vh;
}


.header-content {
  display: flex;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--text-color-primary);
}

/* 统计卡片 */
.stats-cards {
  margin-bottom: 24px;
}

.stat-card {
  background: var(--bg-color-container);
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
}

.stat-icon.success {
  background: linear-gradient(135deg, #67C23A, #85CE61);
}

.stat-icon.danger {
  background: linear-gradient(135deg, #F56C6C, #F78989);
}

.stat-icon.warning {
  background: linear-gradient(135deg, #E6A23C, #EEBE77);
}

.stat-icon.info {
  background: linear-gradient(135deg, #409EFF, #66B1FF);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: var(--text-color-primary);
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: var(--text-color-secondary);
}

/* 图表区域 */
.charts-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.chart-container {
  background: var(--bg-color-container);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chart-header {
  text-align: center;
  margin-bottom: 20px;
}

.chart-header h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color-primary);
}

.chart-header p {
  margin: 0;
  font-size: 14px;
  color: var(--text-color-secondary);
}

.chart {
  width: 100%;
  height: 400px;
  border-radius: 8px;
}

.small-chart {
  height: 300px;
}

.chart-grid {
  margin-top: 0;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .analytics-page {
    padding: 12px;
  }

  .header-content {
    align-items: flex-start;
  }

  .header-left {
    width: 100%;
  }

  .page-title {
    font-size: 18px;
  }

  .stats-cards {
    margin-bottom: 16px;
  }

  .stat-card {
    padding: 16px;
    gap: 12px;
  }

  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }

  .stat-value {
    font-size: 20px;
  }

  .stat-label {
    font-size: 13px;
  }

  .charts-section {
    gap: 16px;
  }

  .chart-container {
    padding: 16px;
  }

  .chart-header h3 {
    font-size: 16px;
  }

  .chart-header p {
    font-size: 13px;
  }

  .chart {
    height: 280px;
  }

  .small-chart {
    height: 240px;
  }

  .chart-grid .el-col {
    margin-bottom: 16px;
  }
}

/* 超小屏幕适配 */
@media (max-width: 480px) {
  .analytics-page {
    padding: 8px;
  }

  .stat-card {
    padding: 12px;
    gap: 8px;
  }

  .stat-icon {
    width: 36px;
    height: 36px;
    font-size: 16px;
  }

  .stat-value {
    font-size: 18px;
  }

  .chart {
    height: 240px;
  }

  .small-chart {
    height: 200px;
  }
}
</style>
