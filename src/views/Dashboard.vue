<template>
  <div class="dashboard">
    <!-- 欢迎横幅 -->
    <div class="welcome-banner">
      <div class="banner-content">
        <div class="welcome-text">
          <h1 class="welcome-title">
            <el-icon class="welcome-icon"><Sunny /></el-icon>
            欢迎回来，{{ displayUserName }}！
          </h1>
          <p class="welcome-subtitle">{{ welcomeMessage }}</p>
          <div class="welcome-meta">
            <span class="current-time">{{ currentTime }}</span>
          </div>
        </div>
        <div class="welcome-illustration">
          <div class="floating-cards">
            <div class="card card-1"></div>
            <div class="card card-2"></div>
            <div class="card card-3"></div>
          </div>
        </div>
      </div>
    </div>



    <!-- 主要内容区域 -->
    <el-row :gutter="24" class="main-content">
      <!-- 最近访问 -->
      <el-col :xs="24" :lg="12">
        <el-card class="recent-visits-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <h3>最近访问</h3>
              <el-button type="primary" size="small" text @click="clearVisitHistory">
                清空
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
          </template>
          <div class="recent-visits-list">
            <div
              v-for="visit in recentVisits"
              :key="visit.id"
              class="visit-item"
              @click="navigateToPage(visit.path)"
            >
              <div class="visit-time">{{ formatVisitTime(visit.visitTime) }}</div>
              <div class="visit-content">
                <div class="visit-icon">
                  <svg-icon
                    v-if="visit.icon && visit.icon !== 'Document'"
                    :name="visit.icon"
                    class="visit-svg-icon"
                  />
                  <el-icon v-else :size="16">
                    <Document />
                  </el-icon>
                </div>
                <div class="visit-text">
                  <span class="visit-title">{{ visit.name }}</span>
                  <span class="visit-path">{{ visit.path }}</span>
                </div>
              </div>
            </div>
            <div v-if="recentVisits.length === 0" class="empty-visits">
              <el-empty description="暂无访问记录" :image-size="60" />
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 登录记录 -->
      <el-col :xs="24" :lg="12">
        <el-card class="login-records-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <h3>最近登录记录</h3>
              <el-button type="primary" size="small" text @click="refreshLoginRecords" :loading="loginRecordsLoading">
                刷新
                <el-icon><ArrowRight /></el-icon>
              </el-button>
            </div>
          </template>
          <div class="login-records-list">
            <div
              v-for="record in loginRecords"
              :key="record.id"
              class="login-record-item"
            >
              <div class="record-time">{{ formatLoginTime(record.loginTime) }}</div>
              <div class="record-content">
                <div class="record-icon" :class="`record-${record.loginStatus === 1 ? 'success' : 'failed'}`">
                  <el-icon :size="16">
                    <component :is="iconMap[record.loginStatus === 1 ? 'Success' : 'Warning']" />
                  </el-icon>
                </div>
                <div class="record-text">
                  <div class="record-title-line">
                    <span class="record-title">{{record.loginStatus === 1 ? '登录成功' : '登录失败'}}</span>
                    <span v-if="record.riskLevel"
                          class="risk-badge"
                          :class="`risk-${record.riskLevel.toLowerCase()}`">
                      {{ record.riskLevelText || getRiskLevelText(record.riskLevel) }}
                    </span>
                  </div>
                  <span class="record-desc">
                    {{ record.clientIp }} · {{ record.location || '未知位置' }}
                    <span v-if="record.deviceType" class="device-info"> · {{ getDeviceInfo(record) }}</span>
                  </span>
                  <div v-if="record.loginStatus === 0" class="failure-info">
                    <span v-if="record.failureReason" class="failure-reason">
                      <el-icon><Warning /></el-icon>
                      {{ record.failureReason }}
                    </span>
                    <span v-if="record.suspiciousReasons && record.suspiciousReasons.length > 0" class="suspicious-reasons">
                      <el-icon><Warning /></el-icon>
                      {{ record.suspiciousReasons.join('、') }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div v-if="loginRecords.length === 0 && !loginRecordsLoading" class="empty-records">
              <el-empty description="暂无登录记录" :image-size="60" />
            </div>
            <div v-if="loginRecordsLoading" class="loading-records">
              <el-skeleton :rows="3" animated />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { getMyLoginRecords } from '@/api/loginLog'
import { getVisitHistory, clearVisitHistory as clearVisitHistoryUtil, formatVisitTime } from '@/utils/visitHistory'
import {
  Sunny,
  ArrowRight,
  Warning,
  SuccessFilled,
  Delete,
  Document
} from '@element-plus/icons-vue'

// Store
const authStore = useAuthStore()
const router = useRouter()

// 图标映射
const iconMap = {
  Warning,
  Success: SuccessFilled,
  Document
}

// 响应式数据
const currentTime = ref('')

// 计算属性
const userInfo = computed(() => authStore.userInfo || {})

const displayUserName = computed(() => {
  // 检查是否已登录
  if (!authStore.isLoggedIn()) {
    return '未登录'
  }

  const info = userInfo.value

  // 如果用户信息还在加载中
  if (!authStore.userInfoLoaded) {
    return '加载中...'
  }

  // 如果用户信息为空或无效
  if (!info || Object.keys(info).length === 0) {
    return '用户'
  }

  // 返回用户名
  return info.username || info.nickname || '用户'
})

const welcomeMessage = computed(() => {
  const hour = new Date().getHours()
  if (hour < 6) return '夜深了，注意休息哦'
  if (hour < 12) return '早上好！新的一天开始了'
  if (hour < 18) return '下午好！工作进展如何？'
  return '晚上好！今天辛苦了'
})

// 登录记录
const loginRecords = ref([])
const loginRecordsLoading = ref(false)

// 最近访问
const recentVisits = ref([])

// 方法
const updateTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}



// 获取登录记录
const fetchLoginRecords = async () => {
  loginRecordsLoading.value = true
  try {
    const records = await getMyLoginRecords()
    loginRecords.value = records || []
  } catch (error) {
    console.error('获取登录记录失败:', error)
    loginRecords.value = []
  } finally {
    loginRecordsLoading.value = false
  }
}

// 刷新登录记录
const refreshLoginRecords = () => {
  fetchLoginRecords()
}

// 格式化登录时间
const formatLoginTime = (time) => {
  if (!time) return ''
  const date = new Date(time)
  const now = new Date()
  const diff = now - date

  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  if (diff < 604800000) return `${Math.floor(diff / 86400000)}天前`

  return date.toLocaleDateString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 获取设备信息
const getDeviceInfo = (record) => {
  const parts = []

  if (record.deviceBrand && record.deviceModel) {
    parts.push(`${record.deviceBrand} ${record.deviceModel}`)
  } else if (record.deviceType) {
    const deviceTypeMap = {
      'DESKTOP': '桌面设备',
      'MOBILE': '移动设备',
      'TABLET': '平板设备'
    }
    parts.push(deviceTypeMap[record.deviceType] || record.deviceType)
  }

  if (record.browserName) {
    parts.push(record.browserName)
  }

  return parts.join(' · ')
}

// 获取风险等级文本
const getRiskLevelText = (riskLevel) => {
  const riskLevelMap = {
    'LOW': '低风险',
    'MEDIUM': '中风险',
    'HIGH': '高风险'
  }
  return riskLevelMap[riskLevel] || riskLevel
}

// 获取最近访问记录
const fetchRecentVisits = () => {
  try {
    recentVisits.value = getVisitHistory() || []
  } catch (error) {
    console.warn('获取访问历史失败:', error)
    recentVisits.value = []
  }
}

// 清空访问历史
const clearVisitHistory = () => {
  try {
    clearVisitHistoryUtil()
    recentVisits.value = []
  } catch (error) {
    console.warn('清空访问历史失败:', error)
  }
}

// 导航到页面
const navigateToPage = (path) => {
  try {
    if (path && router) {
      router.push(path)
    }
  } catch (error) {
    console.warn('页面导航失败:', error)
  }
}



// 生命周期
let timeInterval = null

onMounted(async () => {
  updateTime()
  timeInterval = setInterval(updateTime, 1000)

  // 确保用户信息已加载
  if (!authStore.userInfo && authStore.isLoggedIn()) {
    try {
      await authStore.fetchUserInfo()
    } catch (error) {
      console.error('获取用户信息失败:', error)
    }
  }

  // 如果已登录，获取登录记录和访问历史
  if (authStore.isLoggedIn()) {
    await fetchLoginRecords()
    fetchRecentVisits()
  }
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})
</script>

<style lang="scss" scoped>
.dashboard {
  min-height: 100%;
  
  // 欢迎横幅
  .welcome-banner {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border-radius: 16px;
    padding: 32px;
    margin-bottom: 24px;
    color: white;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 200px;
      height: 200px;
      background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
      border-radius: 50%;
      transform: translate(50%, -50%);
    }

    .banner-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;
      z-index: 1;
    }

    .welcome-text {
      flex: 1;

      .welcome-title {
        font-size: 32px;
        font-weight: 700;
        margin: 0 0 8px 0;
        display: flex;
        align-items: center;
        gap: 12px;

        .welcome-icon {
          font-size: 36px;
          color: #fbbf24;
        }
      }

      .welcome-subtitle {
        font-size: 18px;
        margin: 0 0 16px 0;
        opacity: 0.9;
      }

      .welcome-meta {
        display: flex;
        align-items: center;
        gap: 16px;
        font-size: 14px;
        opacity: 0.8;

        .weather-info {
          display: flex;
          align-items: center;
          gap: 4px;
        }
      }
    }

    .welcome-illustration {
      .floating-cards {
        position: relative;
        width: 120px;
        height: 80px;

        .card {
          position: absolute;
          width: 60px;
          height: 40px;
          background: rgba(255, 255, 255, 0.2);
          border-radius: 8px;
          backdrop-filter: blur(10px);
          animation: float 3s ease-in-out infinite;

          &.card-1 {
            top: 0;
            left: 0;
            animation-delay: 0s;
          }

          &.card-2 {
            top: 20px;
            right: 0;
            animation-delay: 1s;
          }

          &.card-3 {
            bottom: 0;
            left: 30px;
            animation-delay: 2s;
          }
        }
      }
    }
  }



  // 卡片通用样式
  .el-card {
    border-radius: 12px;
    border: 1px solid var(--el-border-color-lighter);
    box-shadow: var(--el-box-shadow-light);
    margin-bottom: 24px;
    background: var(--el-bg-color);

    &:hover {
      box-shadow: var(--el-box-shadow);
    }

    :deep(.el-card__header) {
      padding: 20px 24px;
      border-bottom: 1px solid var(--el-border-color-lighter);
      background: var(--el-bg-color);

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        h3 {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: var(--el-text-color-primary);
        }
      }
    }

    :deep(.el-card__body) {
      padding: 24px;
    }
  }

  // 最近访问
  .recent-visits-card,
  .login-records-card {
    height: 480px;

    :deep(.el-card__body) {
      height: calc(100% - 60px);
      padding: 0;
    }
  }

  .recent-visits-list,
  .login-records-list {
    height: 100%;
    overflow-y: auto;
    padding: 24px;
  }

  .recent-visits-list {
    .visit-item {
      display: flex;
      gap: 16px;
      padding: 16px 0;
      border-bottom: 1px solid var(--el-border-color-lighter);
      cursor: pointer;
      transition: all 0.3s ease;

      &:last-child {
        border-bottom: none;
      }

      &:hover {
        background: var(--el-fill-color-light);
        margin: 0 -16px;
        padding: 16px;
        border-radius: 8px;
      }

      .visit-time {
        font-size: 12px;
        color: var(--el-text-color-placeholder);
        white-space: nowrap;
        min-width: 60px;
      }

      .visit-content {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        flex: 1;

        .visit-icon {
          width: 32px;
          height: 32px;
          border-radius: 6px;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;
          background: var(--el-color-primary-light-9);
          color: var(--el-color-primary);

          .visit-svg-icon {
            width: 16px;
            height: 16px;
            color: var(--el-color-primary);
          }
        }

        .visit-text {
          flex: 1;

          .visit-title {
            font-size: 14px;
            font-weight: 600;
            color: var(--el-text-color-primary);
            display: block;
            margin-bottom: 2px;
          }

          .visit-path {
            font-size: 12px;
            color: var(--el-text-color-placeholder);
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          }
        }
      }
    }

    .empty-visits {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      min-height: 200px;
    }
  }

  // 登录记录
  .login-records-list {
    .login-record-item {
      display: flex;
      gap: 16px;
      padding: 16px 0;
      border-bottom: 1px solid var(--el-border-color-lighter);
      transition: all 0.3s ease;

      &:last-child {
        border-bottom: none;
      }

      &:hover {
        background: var(--el-fill-color-light);
        margin: 0 -16px;
        padding: 16px;
        border-radius: 8px;
      }

      .record-time {
        font-size: 12px;
        color: var(--el-text-color-placeholder);
        white-space: nowrap;
        min-width: 60px;
      }

      .record-content {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        flex: 1;

        .record-icon {
          width: 32px;
          height: 32px;
          border-radius: 6px;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;

          .el-icon {
            color: white;
          }

          &.record-success {
            background: var(--el-color-success);
          }

          &.record-failed {
            background: var(--el-color-warning);
          }
        }

        .record-text {
          .record-title-line {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 2px;

            .record-title {
              font-size: 14px;
              font-weight: 600;
              color: var(--el-text-color-primary);
            }

            .risk-badge {
              font-size: 10px;
              padding: 2px 8px;
              border-radius: 12px;
              font-weight: 600;
              text-transform: uppercase;
              letter-spacing: 0.5px;
              border: none;
              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

              &.risk-low {
                background: linear-gradient(135deg, #10b981, #059669);
                color: white;
              }

              &.risk-medium {
                background: linear-gradient(135deg, #f59e0b, #d97706);
                color: white;
              }

              &.risk-high {
                background: linear-gradient(135deg, #ef4444, #dc2626);
                color: white;
              }
            }
          }

          .record-desc {
            font-size: 13px;
            color: var(--el-text-color-regular);
            display: block;
            margin-bottom: 4px;

            .device-info {
              color: var(--el-text-color-placeholder);
            }
          }

          .failure-info {
            margin-top: 6px;

            .failure-reason,
            .suspicious-reasons {
              display: flex;
              align-items: center;
              gap: 4px;
              font-size: 12px;
              color: var(--el-color-danger);
              margin-bottom: 2px;

              .el-icon {
                font-size: 12px;
              }
            }

            .suspicious-reasons {
              color: var(--el-color-warning);
            }
          }
        }
      }
    }

    .empty-records,
    .loading-records {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      min-height: 200px;
    }
  }




}

// 动画
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .dashboard {
    .welcome-banner {
      padding: 24px 20px;

      .banner-content {
        flex-direction: column;
        text-align: center;
        gap: 20px;
      }

      .welcome-title {
        font-size: 24px;
      }

      .welcome-subtitle {
        font-size: 16px;
      }
    }

    .main-content {
      .el-col {
     
        &:last-child {
          margin-bottom: 0;
        }
      }
    }



    .el-card {
      :deep(.el-card__header) {
        padding: 16px 20px;
      }

      :deep(.el-card__body) {
        padding: 20px;
      }
    }
  }
}
</style>
