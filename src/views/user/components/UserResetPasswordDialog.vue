<template>
  <el-dialog
    v-model="dialogVisible"
    title="重置用户密码"
    :width="dialogWidth"
    :close-on-click-modal="false"
    append-to-body
    class="user-reset-password-dialog"
    @close="handleClose"
  >
    <div class="dialog-content">
      <div class="user-info">
        <el-text type="info" size="small">
          正在为用户 <strong>{{ userData.username }}</strong> 重置密码
        </el-text>
      </div>
      
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        :label-width="labelWidth"
        :label-position="labelPosition"
        @submit.prevent
      >
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="form.newPassword"
            type="password"
            placeholder="请输入新密码"
            maxlength="100"
            show-word-limit
            show-password
            @keyup.enter="handleSubmit"
          />
        </el-form-item>
        
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="form.confirmPassword"
            type="password"
            placeholder="请再次输入新密码"
            maxlength="100"
            show-password
            @keyup.enter="handleSubmit"
          />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :loading="loading"
          @click="handleSubmit"
        >
          确定重置
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, nextTick, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { resetUserPassword } from '@/api/user'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  userData: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const formRef = ref()
const isMobile = ref(false)

// 表单数据
const form = reactive({
  newPassword: '',
  confirmPassword: ''
})

// 自定义验证规则
const validateConfirmPassword = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请再次输入新密码'))
  } else if (value !== form.newPassword) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

// 表单验证规则
const rules = {
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 100, message: '密码长度在 6 到 100 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, validator: validateConfirmPassword, trigger: 'blur' }
  ]
}

// 检查是否为移动端
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

// 计算属性
const dialogWidth = computed(() => {
  return isMobile.value ? '90%' : '500px'
})

const labelWidth = computed(() => {
  return isMobile.value ? '80px' : '100px'
})

const labelPosition = computed(() => {
  return isMobile.value ? 'top' : 'right'
})

// 生命周期
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})

// 监听 visible 变化
watch(
  () => props.visible,
  (newVal) => {
    dialogVisible.value = newVal
    if (newVal) {
      initForm()
    }
  },
  { immediate: true }
)

// 监听 dialogVisible 变化
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// 初始化表单
const initForm = () => {
  nextTick(() => {
    // 重置表单数据
    Object.assign(form, {
      newPassword: '',
      confirmPassword: ''
    })
    
    // 清除验证状态
    if (formRef.value) {
      formRef.value.clearValidate()
    }
  })
}

// 提交表单
const handleSubmit = async () => {
  try {
    // 表单验证
    const valid = await formRef.value.validate()
    if (!valid) return

    loading.value = true

    await resetUserPassword(props.userData.id, form.newPassword)
    ElMessage.success('密码重置成功')
    emit('success')
    handleClose()
  } catch (error) {
    console.error('重置密码失败:', error)
    ElMessage.error('重置密码失败')
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
/* 对话框内容区域 */
.dialog-content {
  padding: 20px 0;
}

/* 用户信息提示 */
.user-info {
  margin-bottom: 20px;
  padding: 12px;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 6px;
}

/* 对话框底部按钮区域 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .dialog-content {
    padding: 16px 0;
  }
  
  .dialog-footer {
    padding-top: 16px;
    gap: 8px;
  }
  
  .dialog-footer .el-button {
    flex: 1;
    min-width: 80px;
  }
}

/* 超小屏幕适配 */
@media (max-width: 480px) {
  .dialog-content {
    padding: 12px 0;
  }
  
  .dialog-footer {
    flex-direction: column-reverse;
    gap: 12px;
  }
  
  .dialog-footer .el-button {
    width: 100%;
  }
}
</style>

<!-- 全局样式 -->
<style>
/* 对话框整体样式优化 */
.user-reset-password-dialog .el-dialog {
  margin: 15vh auto 50px;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.user-reset-password-dialog .el-dialog__header {
  padding: 20px 20px 0 20px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.user-reset-password-dialog .el-dialog__title {
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.user-reset-password-dialog .el-dialog__body {
  padding: 0 20px;
}

.user-reset-password-dialog .el-dialog__footer {
  padding: 0 20px 20px 20px;
  border-top: 1px solid var(--el-border-color-lighter);
}

/* 表单样式优化 */
.user-reset-password-dialog .el-form-item {
  margin-bottom: 20px;
}

.user-reset-password-dialog .el-form-item__label {
  font-weight: 500;
  color: var(--el-text-color-regular);
}

/* 移动端对话框适配 */
@media (max-width: 768px) {
  .user-reset-password-dialog .el-dialog {
    margin: 10vh auto 20px;
    width: 90% !important;
    max-width: none;
  }
  
  .user-reset-password-dialog .el-dialog__header {
    padding: 16px 16px 0 16px;
  }
  
  .user-reset-password-dialog .el-dialog__body {
    padding: 0 16px;
  }
  
  .user-reset-password-dialog .el-dialog__footer {
    padding: 0 16px 16px 16px;
  }
  
  .user-reset-password-dialog .el-form-item {
    margin-bottom: 16px;
  }
}

/* 超小屏幕对话框适配 */
@media (max-width: 480px) {
  .user-reset-password-dialog .el-dialog {
    margin: 5vh auto 10px;
    width: 95% !important;
  }
  
  .user-reset-password-dialog .el-dialog__header {
    padding: 12px 12px 0 12px;
  }
  
  .user-reset-password-dialog .el-dialog__body {
    padding: 0 12px;
  }
  
  .user-reset-password-dialog .el-dialog__footer {
    padding: 0 12px 12px 12px;
  }
  
  .user-reset-password-dialog .el-form-item {
    margin-bottom: 12px;
  }
  
  .user-reset-password-dialog .el-form-item__label {
    font-size: 14px;
    margin-bottom: 8px;
  }
}
</style>
