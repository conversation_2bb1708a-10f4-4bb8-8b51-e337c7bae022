<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑用户类型' : '新增用户类型'"
    :width="dialogWidth"
    :close-on-click-modal="false"
    append-to-body
    class="user-type-dialog"
    @close="handleClose"
  >
    <div class="dialog-content">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        :label-width="labelWidth"
        :label-position="labelPosition"
        @submit.prevent
      >
      <el-form-item label="类型名称" prop="typeName">
        <el-input
          v-model="form.typeName"
          placeholder="请输入类型名称"
          maxlength="100"
          show-word-limit
          @keyup.enter="handleSubmit"
        />
      </el-form-item>
      
      <el-form-item label="是否内置" prop="isBuiltin">
        <el-radio-group v-model="form.isBuiltin">
          <el-radio :value="0">内置</el-radio>
          <el-radio :value="1">非内置</el-radio>
        </el-radio-group>
        <div class="form-tip">
          <el-text type="info" size="small">
            内置类型通常用于系统核心功能，非内置类型用于业务扩展
          </el-text>
        </div>
      </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :loading="loading"
          @click="handleSubmit"
        >
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, nextTick, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { createUserType, updateUserType } from '@/api/userType'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  formData: {
    type: Object,
    default: () => ({})
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const formRef = ref()
const isMobile = ref(false)

// 表单数据
const form = reactive({
  typeName: '',
  isBuiltin: 1
})

// 表单验证规则
const rules = {
  typeName: [
    { required: true, message: '请输入类型名称', trigger: 'blur' },
    { min: 1, max: 100, message: '类型名称长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  isBuiltin: [
    { required: true, message: '请选择是否内置', trigger: 'change' }
  ]
}

// 检查是否为移动端
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

// 计算属性
const dialogWidth = computed(() => {
  return isMobile.value ? '90%' : '500px'
})

const labelWidth = computed(() => {
  return isMobile.value ? '80px' : '100px'
})

const labelPosition = computed(() => {
  return isMobile.value ? 'top' : 'right'
})

// 生命周期
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})

// 监听 visible 变化
watch(
  () => props.visible,
  (newVal) => {
    dialogVisible.value = newVal
    if (newVal) {
      initForm()
    }
  },
  { immediate: true }
)

// 监听 dialogVisible 变化
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// 初始化表单
const initForm = () => {
  nextTick(() => {
    if (props.isEdit && props.formData) {
      Object.assign(form, {
        typeName: props.formData.typeName || '',
        isBuiltin: props.formData.isBuiltin ?? 1
      })
    } else {
      Object.assign(form, {
        typeName: '',
        isBuiltin: 1
      })
    }
    
    // 清除验证状态
    if (formRef.value) {
      formRef.value.clearValidate()
    }
  })
}

// 提交表单
const handleSubmit = async () => {
  try {
    // 表单验证
    const valid = await formRef.value.validate()
    if (!valid) return

    loading.value = true

    let response
    if (props.isEdit) {
      // 编辑
      response = await updateUserType(props.formData.id, form)
    } else {
      // 新增
      response = await createUserType(form)
    }

    if (response.success) {
      ElMessage.success(props.isEdit ? '更新成功' : '创建成功')
      emit('success')
    }
  } catch (error) {
    // 错误提示由响应拦截器处理，这里只需要记录日志
    console.error('提交失败:', error)
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
/* 对话框内容区域 */
.dialog-content {
  padding: 20px 0;
}

.form-tip {
  margin-top: 4px;
}

/* 对话框底部按钮区域 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .dialog-content {
    padding: 16px 0;
  }

  .dialog-footer {
    padding-top: 16px;
    gap: 8px;
  }

  .dialog-footer .el-button {
    flex: 1;
    min-width: 80px;
  }
}

/* 超小屏幕适配 */
@media (max-width: 480px) {
  .dialog-content {
    padding: 12px 0;
  }

  .dialog-footer {
    flex-direction: column-reverse;
    gap: 12px;
  }

  .dialog-footer .el-button {
    width: 100%;
  }
}
</style>

<!-- 全局样式 -->
<style>
/* 对话框整体样式优化 */
.user-type-dialog .el-dialog {
  margin: 15vh auto 50px;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.user-type-dialog .el-dialog__header {
  padding: 20px 20px 0 20px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.user-type-dialog .el-dialog__title {
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.user-type-dialog .el-dialog__body {
  padding: 0 20px;
}

.user-type-dialog .el-dialog__footer {
  padding: 0 20px 20px 20px;
  border-top: 1px solid var(--el-border-color-lighter);
}

/* 表单样式优化 */
.user-type-dialog .el-form-item {
  margin-bottom: 20px;
}

.user-type-dialog .el-form-item__label {
  font-weight: 500;
  color: var(--el-text-color-regular);
}

/* 移动端对话框适配 */
@media (max-width: 768px) {
  .user-type-dialog .el-dialog {
    margin: 10vh auto 20px;
    width: 90% !important;
    max-width: none;
  }

  .user-type-dialog .el-dialog__header {
    padding: 16px 16px 0 16px;
  }

  .user-type-dialog .el-dialog__body {
    padding: 0 16px;
  }

  .user-type-dialog .el-dialog__footer {
    padding: 0 16px 16px 16px;
  }

  .user-type-dialog .el-form-item {
    margin-bottom: 16px;
  }
}

/* 超小屏幕对话框适配 */
@media (max-width: 480px) {
  .user-type-dialog .el-dialog {
    margin: 5vh auto 10px;
    width: 95% !important;
  }

  .user-type-dialog .el-dialog__header {
    padding: 12px 12px 0 12px;
  }

  .user-type-dialog .el-dialog__body {
    padding: 0 12px;
  }

  .user-type-dialog .el-dialog__footer {
    padding: 0 12px 12px 12px;
  }

  .user-type-dialog .el-form-item {
    margin-bottom: 12px;
  }

  .user-type-dialog .el-form-item__label {
    font-size: 14px;
    margin-bottom: 8px;
  }
}
</style>
