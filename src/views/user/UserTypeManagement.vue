<template>
  <div class="management-page">
    <!-- 搜索和操作区域 -->
    <div class="search-section">
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="类型名称">
          <el-input
            v-model="searchForm.typeName"
            placeholder="请输入类型名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="是否内置">
          <el-select
            v-model="searchForm.isBuiltin"
            placeholder="请选择是否内置"
            clearable
            style="width: 150px"
          >
            <el-option label="内置" :value="0"></el-option>
            <el-option label="非内置" :value="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>

      <div class="action-buttons">
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          新增用户类型
        </el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        :header-cell-style="{ background: 'var(--bg-color-table-header)', color: 'var(--text-color-primary)', fontWeight: '600' }"
        :row-style="{ height: '56px' }"
      >
        <el-table-column prop="typeName" label="类型名称" min-width="80" align="center" header-align="center">
          <template #default="{ row }">
            <span>{{ row.typeName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="isBuiltin" label="是否内置" width="100" align="center" header-align="center">
          <template #default="{ row }">
            <el-tag :type="row.isBuiltin === 0 ? 'warning' : 'info'" size="small">
              {{ row.isBuiltin === 0 ? '内置' : '非内置' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="userCount" label="关联用户数" width="120" align="center" header-align="center">
          <template #default="{ row }">
            <el-text type="primary">{{ row.userCount || 0 }}</el-text>
          </template>
        </el-table-column>
        <el-table-column prop="menuCount" label="菜单数量" width="120" align="center" header-align="center">
          <template #default="{ row }">
            <el-text type="success">{{ row.menuCount || 0 }}</el-text>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" align="center" header-align="center">
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="更新时间" width="180" align="center" header-align="center">
          <template #default="{ row }">
            {{ formatDateTime(row.updateTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="220" fixed="right" align="center" header-align="center">
          <template #default="{ row }">
            <template v-if="!row.superAdminType">
              <div class="action-buttons-row">
                <el-tooltip content="分配权限" placement="top">
                  <el-button
                    type="primary"
                    size="small"
                    @click="handleAssignMenus(row)"
                    :icon="Setting"
                    circle
                  />
                </el-tooltip>
                <el-tooltip content="编辑" placement="top">
                  <el-button
                    type="warning"
                    size="small"
                    @click="handleEdit(row)"
                    :icon="Edit"
                    circle
                  />
                </el-tooltip>
                <template v-if="row.isBuiltin !== 0">
                  <el-tooltip content="删除" placement="top">
                    <el-button
                      type="danger"
                      size="small"
                      @click="handleDelete(row)"
                      :icon="Delete"
                      circle
                    />
                  </el-tooltip>
                </template>
              </div>
            </template>
            <template v-else>
              <el-text type="info" size="small">该类型不支持被操作</el-text>
            </template>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <Pagination
        v-model:current="pagination.current"
        v-model:pageSize="pagination.size"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <UserTypeDialog
      v-model:visible="dialogVisible"
      :form-data="currentUserType"
      :is-edit="isEdit"
      @success="handleDialogSuccess"
    />

    <!-- 权限分配对话框 -->
    <MenuAssignDialog
      v-model:visible="menuDialogVisible"
      :user-type="currentUserType"
      @success="handleMenuAssignSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Plus, Setting, Edit, Delete } from '@element-plus/icons-vue'
import { getUserTypePage, deleteUserType } from '@/api/userType'
import { formatDateTime } from '@/utils/date'
import UserTypeDialog from './components/UserTypeDialog.vue'
import MenuAssignDialog from '../menu/components/MenuAssignDialog.vue'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const dialogVisible = ref(false)
const menuDialogVisible = ref(false)
const isEdit = ref(false)
const currentUserType = ref({})

// 搜索表单
const searchForm = reactive({
  typeName: '',
  isBuiltin: null
})

// 分页数据
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 获取用户类型列表
const fetchUserTypes = async () => {
  try {
    loading.value = true
    const params = {
      current: pagination.current,
      size: pagination.size,
      ...searchForm
    }
    
    const response = await getUserTypePage(params)
    tableData.value = response.records || []
    pagination.total = response.total || 0
  } catch (error) {
    console.error('获取用户类型列表失败:', error)
    ElMessage.error('获取用户类型列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  fetchUserTypes()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    typeName: '',
    isBuiltin: null
  })
  pagination.current = 1
  fetchUserTypes()
}

// 新增
const handleAdd = () => {
  currentUserType.value = {}
  isEdit.value = false
  dialogVisible.value = true
}

// 编辑
const handleEdit = (row) => {
  currentUserType.value = { ...row }
  isEdit.value = true
  dialogVisible.value = true
}

// 删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户类型"${row.typeName}"吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await deleteUserType(row.id)
    if (response.success) {
      ElMessage.success('删除成功')
      fetchUserTypes()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除用户类型失败:', error)
    }
  }
}

// 分配权限
const handleAssignMenus = (row) => {
  currentUserType.value = { ...row }
  menuDialogVisible.value = true
}


// 分页大小变化
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1
  fetchUserTypes()
}

// 当前页变化
const handleCurrentChange = (current) => {
  pagination.current = current
  fetchUserTypes()
}

// 对话框成功回调
const handleDialogSuccess = () => {
  dialogVisible.value = false
  fetchUserTypes()
}

// 权限分配成功回调
const handleMenuAssignSuccess = () => {
  menuDialogVisible.value = false
  fetchUserTypes()
}

// 初始化
onMounted(() => {
  fetchUserTypes()
})
</script>

<style scoped>
/* 使用全局统一的管理页面样式 */

/* 响应式设计 */
@media (max-width: 768px) {
  .management-page {
    padding: 16px;
  }

  .search-form :deep(.el-form) {
    flex-direction: column;
  }

  .search-form :deep(.el-form-item) {
    margin-right: 0;
    margin-bottom: 16px;
  }
}

/* 移动端响应式样式 */
@media (max-width: 768px) {
  .search-form {
    .el-form-item {
      width: 100%;
      margin-right: 0;
      margin-bottom: 16px;

      .el-input,
      .el-select {
        width: 100% !important;
      }
    }

    /* 搜索按钮区域 */
    .el-form-item:last-child {
      display: flex;
      gap: 8px;

      .el-button {
        flex: 1;
      }
    }
  }

  .action-buttons {
    flex-direction: column;
    gap: 12px;

    .el-button {
      width: 100%;
      margin-left: 0;
    }
  }
}

</style>
