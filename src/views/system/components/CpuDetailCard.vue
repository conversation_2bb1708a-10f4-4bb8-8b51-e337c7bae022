<template>
  <div class="detail-card cpu-detail-card">
    <div class="card-header">
      <div class="card-title">
        <el-icon class="card-icon"><Cpu /></el-icon>
        <span>CPU详细信息</span>
      </div>
    </div>
    
    <div class="card-content">
      <!-- CPU基本信息 -->
      <div class="info-section">
        <h4 class="section-title">基本信息</h4>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">型号:</span>
            <span class="value" :title="cpuInfo?.model">{{ cpuInfo?.model || 'N/A' }}</span>
          </div>
          <div class="info-item">
            <span class="label">供应商:</span>
            <span class="value">{{ cpuInfo?.vendor || 'N/A' }}</span>
          </div>
          <div class="info-item">
            <span class="label">频率:</span>
            <span class="value">{{ formatFrequency(cpuInfo?.frequency) }}</span>
          </div>
          <div class="info-item">
            <span class="label">物理核心:</span>
            <span class="value">{{ cpuInfo?.physicalCores || 0 }} 核</span>
          </div>
          <div class="info-item">
            <span class="label">逻辑核心:</span>
            <span class="value">{{ cpuInfo?.logicalCores || 0 }} 核</span>
          </div>
          <div class="info-item">
            <span class="label">缓存大小:</span>
            <span class="value">{{ formatCacheSize(cpuInfo?.cacheSize) }}</span>
          </div>
          <div class="info-item">
            <span class="label">架构:</span>
            <span class="value">{{ cpuInfo?.architecture || 'N/A' }}</span>
          </div>
          <div class="info-item">
            <span class="label">系列:</span>
            <span class="value">{{ cpuInfo?.family || 'N/A' }}</span>
          </div>
          <div class="info-item">
            <span class="label">步进:</span>
            <span class="value">{{ cpuInfo?.stepping || 'N/A' }}</span>
          </div>
        </div>
      </div>

      <!-- CPU使用率详情 -->
      <div class="info-section">
        <h4 class="section-title">使用率详情</h4>
        <div class="usage-chart">
          <div ref="cpuUsageChart" class="chart-container"></div>
        </div>
        <div class="usage-details">
          <div class="usage-item">
            <div class="usage-label">
              <span class="color-indicator user-color"></span>
              <span>用户态</span>
            </div>
            <span class="usage-value">{{ cpuInfo?.usage?.userPercent?.toFixed(1) || 0 }}%</span>
          </div>
          <div class="usage-item">
            <div class="usage-label">
              <span class="color-indicator system-color"></span>
              <span>系统态</span>
            </div>
            <span class="usage-value">{{ cpuInfo?.usage?.systemPercent?.toFixed(1) || 0 }}%</span>
          </div>
          <div class="usage-item">
            <div class="usage-label">
              <span class="color-indicator idle-color"></span>
              <span>空闲</span>
            </div>
            <span class="usage-value">{{ cpuInfo?.usage?.idlePercent?.toFixed(1) || 0 }}%</span>
          </div>
          <div class="usage-item">
            <div class="usage-label">
              <span class="color-indicator wait-color"></span>
              <span>等待I/O</span>
            </div>
            <span class="usage-value">{{ cpuInfo?.usage?.waitPercent?.toFixed(1) || 0 }}%</span>
          </div>
          <div class="usage-item">
            <div class="usage-label">
              <span class="color-indicator irq-color"></span>
              <span>中断</span>
            </div>
            <span class="usage-value">{{ cpuInfo?.usage?.irqPercent?.toFixed(1) || 0 }}%</span>
          </div>
          <div class="usage-item">
            <div class="usage-label">
              <span class="color-indicator soft-irq-color"></span>
              <span>软中断</span>
            </div>
            <span class="usage-value">{{ cpuInfo?.usage?.softIrqPercent?.toFixed(1) || 0 }}%</span>
          </div>
        </div>
      </div>

      <!-- 系统负载 -->
      <div class="info-section">
        <h4 class="section-title">系统负载</h4>
        <div class="load-chart">
          <div ref="loadChart" class="chart-container"></div>
        </div>
        <div class="load-details">
          <div class="load-item">
            <span class="load-label">1分钟平均负载</span>
            <span class="load-value" :class="getLoadClass(cpuInfo?.loadAverage?.oneMinute)">
              {{ cpuInfo?.loadAverage?.oneMinute?.toFixed(2) || 0 }}
            </span>
          </div>
          <div class="load-item">
            <span class="load-label">5分钟平均负载</span>
            <span class="load-value" :class="getLoadClass(cpuInfo?.loadAverage?.fiveMinutes)">
              {{ cpuInfo?.loadAverage?.fiveMinutes?.toFixed(2) || 0 }}
            </span>
          </div>
          <div class="load-item">
            <span class="load-label">15分钟平均负载</span>
            <span class="load-value" :class="getLoadClass(cpuInfo?.loadAverage?.fifteenMinutes)">
              {{ cpuInfo?.loadAverage?.fifteenMinutes?.toFixed(2) || 0 }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick } from 'vue'
import { Cpu } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

// Props
const props = defineProps({
  cpuInfo: {
    type: Object,
    default: () => ({})
  }
})

// 图表引用
const cpuUsageChart = ref(null)
const loadChart = ref(null)
let cpuUsageChartInstance = null
let loadChartInstance = null

// 格式化频率
const formatFrequency = (frequency) => {
  if (!frequency) return 'N/A'
  if (frequency >= 1000) {
    return `${(frequency / 1000).toFixed(1)} GHz`
  }
  return `${frequency} MHz`
}

// 格式化缓存大小
const formatCacheSize = (cacheSize) => {
  if (!cacheSize) return 'N/A'
  if (cacheSize >= 1024) {
    return `${(cacheSize / 1024).toFixed(1)} MB`
  }
  return `${cacheSize} KB`
}

// 获取负载状态类名
const getLoadClass = (load) => {
  const cores = props.cpuInfo?.logicalCores || 1
  const loadPercent = (load / cores) * 100
  if (loadPercent >= 80) return 'load-danger'
  if (loadPercent >= 60) return 'load-warning'
  return 'load-normal'
}

// 初始化CPU使用率图表
const initCpuUsageChart = () => {
  if (!cpuUsageChart.value) return
  
  cpuUsageChartInstance = echarts.init(cpuUsageChart.value)
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c}% ({d}%)'
    },
    series: [
      {
        name: 'CPU使用率',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '50%'],
        data: [
          { value: props.cpuInfo?.usage?.userPercent || 0, name: '用户态', itemStyle: { color: '#409eff' } },
          { value: props.cpuInfo?.usage?.systemPercent || 0, name: '系统态', itemStyle: { color: '#67c23a' } },
          { value: props.cpuInfo?.usage?.waitPercent || 0, name: '等待I/O', itemStyle: { color: '#e6a23c' } },
          { value: props.cpuInfo?.usage?.irqPercent || 0, name: '中断', itemStyle: { color: '#f56c6c' } },
          { value: props.cpuInfo?.usage?.softIrqPercent || 0, name: '软中断', itemStyle: { color: '#909399' } },
          { value: props.cpuInfo?.usage?.idlePercent || 0, name: '空闲', itemStyle: { color: '#f0f0f0' } }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        label: {
          show: false
        },
        labelLine: {
          show: false
        }
      }
    ]
  }
  
  cpuUsageChartInstance.setOption(option)
}

// 初始化负载图表
const initLoadChart = () => {
  if (!loadChart.value) return
  
  loadChartInstance = echarts.init(loadChart.value)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'category',
      data: ['1分钟', '5分钟', '15分钟'],
      axisLabel: {
        fontSize: 12
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        fontSize: 12
      }
    },
    series: [
      {
        name: '系统负载',
        type: 'bar',
        data: [
          props.cpuInfo?.loadAverage?.oneMinute || 0,
          props.cpuInfo?.loadAverage?.fiveMinutes || 0,
          props.cpuInfo?.loadAverage?.fifteenMinutes || 0
        ],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#409eff' },
            { offset: 1, color: '#67c23a' }
          ])
        },
        barWidth: '60%'
      }
    ]
  }
  
  loadChartInstance.setOption(option)
}

// 更新图表数据
const updateCharts = () => {
  if (cpuUsageChartInstance && props.cpuInfo?.usage) {
    const option = {
      series: [
        {
          data: [
            { value: props.cpuInfo.usage.userPercent || 0, name: '用户态', itemStyle: { color: '#409eff' } },
            { value: props.cpuInfo.usage.systemPercent || 0, name: '系统态', itemStyle: { color: '#67c23a' } },
            { value: props.cpuInfo.usage.waitPercent || 0, name: '等待I/O', itemStyle: { color: '#e6a23c' } },
            { value: props.cpuInfo.usage.irqPercent || 0, name: '中断', itemStyle: { color: '#f56c6c' } },
            { value: props.cpuInfo.usage.softIrqPercent || 0, name: '软中断', itemStyle: { color: '#909399' } },
            { value: props.cpuInfo.usage.idlePercent || 0, name: '空闲', itemStyle: { color: '#f0f0f0' } }
          ]
        }
      ]
    }
    cpuUsageChartInstance.setOption(option)
  }
  
  if (loadChartInstance && props.cpuInfo?.loadAverage) {
    const option = {
      series: [
        {
          data: [
            props.cpuInfo.loadAverage.oneMinute || 0,
            props.cpuInfo.loadAverage.fiveMinutes || 0,
            props.cpuInfo.loadAverage.fifteenMinutes || 0
          ]
        }
      ]
    }
    loadChartInstance.setOption(option)
  }
}

// 监听数据变化
watch(() => props.cpuInfo, () => {
  nextTick(() => {
    updateCharts()
  })
}, { deep: true })

// 生命周期
onMounted(() => {
  nextTick(() => {
    initCpuUsageChart()
    initLoadChart()
  })
})
</script>

<style scoped>
.detail-card {
  background: var(--el-bg-color);
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--el-border-color-lighter);
  height: fit-content;
}

.card-header {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--el-border-color-extra-light);
}

.card-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.card-icon {
  font-size: 20px;
  color: #67c23a;
  margin-right: 8px;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.info-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 8px 0;
  padding-bottom: 4px;
  border-bottom: 2px solid var(--el-color-primary);
  display: inline-block;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: var(--el-bg-color-page);
  border-radius: 6px;
  border: 1px solid var(--el-border-color-extra-light);
}

.info-item .label {
  font-size: 13px;
  color: var(--el-text-color-regular);
  font-weight: 500;
}

.info-item .value {
  font-size: 13px;
  color: var(--el-text-color-primary);
  font-weight: 600;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.chart-container {
  height: 200px;
  width: 100%;
}

.usage-details {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
}

.usage-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 8px;
  background: var(--el-bg-color-page);
  border-radius: 4px;
}

.usage-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: var(--el-text-color-regular);
}

.color-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.user-color {
  background-color: #409eff;
}

.system-color {
  background-color: #67c23a;
}

.idle-color {
  background-color: #f0f0f0;
}

.wait-color {
  background-color: #e6a23c;
}

.irq-color {
  background-color: #f56c6c;
}

.soft-irq-color {
  background-color: #909399;
}

.usage-value {
  font-size: 12px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.load-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.load-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: var(--el-bg-color-page);
  border-radius: 6px;
  border: 1px solid var(--el-border-color-extra-light);
}

.load-label {
  font-size: 13px;
  color: var(--el-text-color-regular);
}

.load-value {
  font-size: 14px;
  font-weight: 600;
}

.load-normal {
  color: #67c23a;
}

.load-warning {
  color: #e6a23c;
}

.load-danger {
  color: #f56c6c;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detail-card {
    padding: 20px;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .usage-details {
    grid-template-columns: repeat(2, 1fr);
  }

  .chart-container {
    height: 180px;
  }
}

@media (max-width: 480px) {
  .detail-card {
    padding: 16px;
  }

  .card-title {
    font-size: 16px;
  }

  .usage-details {
    grid-template-columns: 1fr;
  }

  .chart-container {
    height: 160px;
  }
}
</style>
