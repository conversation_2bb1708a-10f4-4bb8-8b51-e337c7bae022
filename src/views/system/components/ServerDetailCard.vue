<template>
  <div class="detail-card server-detail-card">
    <div class="card-header">
      <div class="card-title">
        <el-icon class="card-icon"><Monitor /></el-icon>
        <span>服务器详细信息</span>
      </div>
    </div>
    
    <div class="card-content">
      <!-- 基本信息 -->
      <div class="info-section">
        <h4 class="section-title">基本信息</h4>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">主机名:</span>
            <span class="value">{{ serverInfo?.hostname || 'N/A' }}</span>
          </div>
          <div class="info-item">
            <span class="label">操作系统:</span>
            <span class="value">{{ serverInfo?.osName || 'N/A' }}</span>
          </div>
          <div class="info-item">
            <span class="label">系统版本:</span>
            <span class="value" :title="serverInfo?.osVersion">{{ formatVersion(serverInfo?.osVersion) }}</span>
          </div>
          <div class="info-item">
            <span class="label">系统架构:</span>
            <span class="value">{{ serverInfo?.systemArch || 'N/A' }}</span>
          </div>
          <div class="info-item">
            <span class="label">处理器架构:</span>
            <span class="value">{{ serverInfo?.osArch || 'N/A' }}</span>
          </div>
          <div class="info-item">
            <span class="label">时区:</span>
            <span class="value">{{ serverInfo?.timeZone || 'N/A' }}</span>
          </div>
          <div class="info-item">
            <span class="label">系统制造商:</span>
            <span class="value">{{ serverInfo?.osManufacturer || 'N/A' }}</span>
          </div>
          <div class="info-item">
            <span class="label">构建号:</span>
            <span class="value">{{ serverInfo?.osBuildNumber || 'N/A' }}</span>
          </div>
          <div class="info-item">
            <span class="label">进程数:</span>
            <span class="value">{{ serverInfo?.processCount || 0 }}</span>
          </div>
          <div class="info-item">
            <span class="label">线程数:</span>
            <span class="value">{{ serverInfo?.threadCount || 0 }}</span>
          </div>
        </div>
      </div>

      <!-- 网络信息 -->
      <div class="info-section">
        <h4 class="section-title">网络信息</h4>
        <div class="network-info">
          <div class="ip-section">
            <div class="ip-item primary">
              <div class="ip-header">
                <el-icon class="ip-icon"><Connection /></el-icon>
                <span class="ip-label">内网IP</span>
              </div>
              <span class="ip-value">{{ serverInfo?.internalIp || 'N/A' }}</span>
            </div>
            <div class="ip-item secondary">
              <div class="ip-header">
                <el-icon class="ip-icon"><Link /></el-icon>
                <span class="ip-label">外网IP</span>
              </div>
              <span class="ip-value">{{ serverInfo?.externalIp || 'N/A' }}</span>
            </div>
          </div>
          
          <div v-if="serverInfo?.ipAddresses && serverInfo.ipAddresses.length > 0" class="all-ips">
            <div class="all-ips-header">
              <span class="all-ips-label">所有IP地址</span>
            </div>
            <div class="ip-list">
              <el-tag 
                v-for="(ip, index) in serverInfo.ipAddresses" 
                :key="index"
                size="small"
                type="info"
                class="ip-tag"
              >
                {{ ip }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- 运行时间信息 -->
      <div class="info-section">
        <h4 class="section-title">运行时间</h4>
        <div class="uptime-info">
          <div class="uptime-chart">
            <div ref="uptimeChart" class="chart-container"></div>
          </div>
          <div class="uptime-details">
            <div class="uptime-item">
              <div class="uptime-header">
                <el-icon class="uptime-icon"><Timer /></el-icon>
                <span class="uptime-label">系统启动时间</span>
              </div>
              <span class="uptime-value">{{ formatTime(serverInfo?.systemBootTime) }}</span>
            </div>
            <div class="uptime-item">
              <div class="uptime-header">
                <el-icon class="uptime-icon"><Clock /></el-icon>
                <span class="uptime-label">系统运行时长</span>
              </div>
              <span class="uptime-value highlight">{{ serverInfo?.systemUptimeFormatted || 'N/A' }}</span>
            </div>
            <div class="uptime-item">
              <div class="uptime-header">
                <el-icon class="uptime-icon"><Calendar /></el-icon>
                <span class="uptime-label">当前系统时间</span>
              </div>
              <span class="uptime-value">{{ formatTime(serverInfo?.currentTime) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 系统状态指示器 -->
      <div class="info-section">
        <h4 class="section-title">系统状态</h4>
        <div class="status-indicators">
          <div class="status-item">
            <div class="status-icon online">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="status-info">
              <span class="status-label">系统状态</span>
              <span class="status-value online">运行正常</span>
            </div>
          </div>
          <div class="status-item">
            <div class="status-icon" :class="getUptimeStatusClass()">
              <el-icon><Timer /></el-icon>
            </div>
            <div class="status-info">
              <span class="status-label">运行时长</span>
              <span class="status-value" :class="getUptimeStatusClass()">
                {{ getUptimeStatus() }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick } from 'vue'
import {
  Monitor, Connection, Link, Timer, Clock, Calendar,
  CircleCheck
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'

// Props
const props = defineProps({
  serverInfo: {
    type: Object,
    default: () => ({})
  }
})

// 图表引用
const uptimeChart = ref(null)
let uptimeChartInstance = null

// 格式化版本信息
const formatVersion = (version) => {
  if (!version) return 'N/A'
  if (version.length > 25) {
    return version.substring(0, 22) + '...'
  }
  return version
}

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return 'N/A'
  return new Date(timeStr).toLocaleString('zh-CN')
}

// 获取运行时长状态
const getUptimeStatus = () => {
  const uptimeSeconds = props.serverInfo?.systemUptimeSeconds || 0
  const days = Math.floor(uptimeSeconds / (24 * 3600))
  
  if (days >= 30) return '长期稳定'
  if (days >= 7) return '稳定运行'
  if (days >= 1) return '正常运行'
  return '刚启动'
}

// 获取运行时长状态类名
const getUptimeStatusClass = () => {
  const uptimeSeconds = props.serverInfo?.systemUptimeSeconds || 0
  const days = Math.floor(uptimeSeconds / (24 * 3600))
  
  if (days >= 30) return 'excellent'
  if (days >= 7) return 'good'
  if (days >= 1) return 'normal'
  return 'warning'
}

// 初始化运行时间图表
const initUptimeChart = () => {
  if (!uptimeChart.value) return
  
  uptimeChartInstance = echarts.init(uptimeChart.value)
  
  const uptimeSeconds = props.serverInfo?.systemUptimeSeconds || 0
  const days = Math.floor(uptimeSeconds / (24 * 3600))
  const hours = Math.floor((uptimeSeconds % (24 * 3600)) / 3600)
  const minutes = Math.floor((uptimeSeconds % 3600) / 60)
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [
      {
        name: '运行时间分布',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '50%'],
        data: [
          { 
            value: days, 
            name: '天数', 
            itemStyle: { color: '#409eff' }
          },
          { 
            value: hours, 
            name: '小时', 
            itemStyle: { color: '#67c23a' }
          },
          { 
            value: minutes, 
            name: '分钟', 
            itemStyle: { color: '#e6a23c' }
          }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        label: {
          show: true,
          position: 'outside',
          formatter: '{b}: {c}',
          fontSize: 12
        }
      }
    ]
  }
  
  uptimeChartInstance.setOption(option)
}

// 更新图表数据
const updateChart = () => {
  if (!uptimeChartInstance || !props.serverInfo?.systemUptimeSeconds) return
  
  const uptimeSeconds = props.serverInfo.systemUptimeSeconds
  const days = Math.floor(uptimeSeconds / (24 * 3600))
  const hours = Math.floor((uptimeSeconds % (24 * 3600)) / 3600)
  const minutes = Math.floor((uptimeSeconds % 3600) / 60)
  
  const option = {
    series: [
      {
        data: [
          { 
            value: days, 
            name: '天数', 
            itemStyle: { color: '#409eff' }
          },
          { 
            value: hours, 
            name: '小时', 
            itemStyle: { color: '#67c23a' }
          },
          { 
            value: minutes, 
            name: '分钟', 
            itemStyle: { color: '#e6a23c' }
          }
        ]
      }
    ]
  }
  
  uptimeChartInstance.setOption(option)
}

// 监听数据变化
watch(() => props.serverInfo, () => {
  nextTick(() => {
    updateChart()
  })
}, { deep: true })

// 生命周期
onMounted(() => {
  nextTick(() => {
    initUptimeChart()
  })
})
</script>

<style scoped>
.detail-card {
  background: var(--el-bg-color);
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--el-border-color-lighter);
  height: fit-content;
}

.card-header {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--el-border-color-extra-light);
}

.card-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.card-icon {
  font-size: 20px;
  color: #909399;
  margin-right: 8px;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.info-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 8px 0;
  padding-bottom: 4px;
  border-bottom: 2px solid var(--el-color-primary);
  display: inline-block;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 8px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: var(--el-bg-color-page);
  border-radius: 6px;
  border: 1px solid var(--el-border-color-extra-light);
}

.info-item .label {
  font-size: 13px;
  color: var(--el-text-color-regular);
  font-weight: 500;
  min-width: 80px;
}

.info-item .value {
  font-size: 13px;
  color: var(--el-text-color-primary);
  font-weight: 600;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: right;
}

.network-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.ip-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.ip-item {
  padding: 12px;
  background: var(--el-bg-color-page);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-extra-light);
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.ip-item.primary {
  border-left: 4px solid #409eff;
}

.ip-item.secondary {
  border-left: 4px solid #67c23a;
}

.ip-header {
  display: flex;
  align-items: center;
  gap: 6px;
}

.ip-icon {
  font-size: 14px;
  color: var(--el-color-primary);
}

.ip-label {
  font-size: 12px;
  color: var(--el-text-color-regular);
  font-weight: 500;
}

.ip-value {
  font-size: 14px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.all-ips {
  padding: 12px;
  background: var(--el-bg-color-page);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-extra-light);
}

.all-ips-header {
  margin-bottom: 8px;
}

.all-ips-label {
  font-size: 12px;
  color: var(--el-text-color-regular);
  font-weight: 500;
}

.ip-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.ip-tag {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.uptime-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.chart-container {
  height: 180px;
  width: 100%;
}

.uptime-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.uptime-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 12px;
  background: var(--el-bg-color-page);
  border-radius: 6px;
  border: 1px solid var(--el-border-color-extra-light);
}

.uptime-header {
  display: flex;
  align-items: center;
  gap: 6px;
}

.uptime-icon {
  font-size: 14px;
  color: var(--el-color-primary);
}

.uptime-label {
  font-size: 13px;
  color: var(--el-text-color-regular);
  font-weight: 500;
}

.uptime-value {
  font-size: 13px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.uptime-value.highlight {
  color: var(--el-color-primary);
  font-size: 14px;
}

.status-indicators {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: var(--el-bg-color-page);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-extra-light);
}

.status-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.status-icon.online {
  background: #f0f9ff;
  color: #67c23a;
}

.status-icon.excellent {
  background: #f0f9ff;
  color: #67c23a;
}

.status-icon.good {
  background: #fdf6ec;
  color: #409eff;
}

.status-icon.normal {
  background: #fef0e6;
  color: #e6a23c;
}

.status-icon.warning {
  background: #fef0f0;
  color: #f56c6c;
}

.status-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.status-label {
  font-size: 12px;
  color: var(--el-text-color-regular);
}

.status-value {
  font-size: 13px;
  font-weight: 600;
}

.status-value.online {
  color: #67c23a;
}

.status-value.excellent {
  color: #67c23a;
}

.status-value.good {
  color: #409eff;
}

.status-value.normal {
  color: #e6a23c;
}

.status-value.warning {
  color: #f56c6c;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detail-card {
    padding: 20px;
  }

  .ip-section {
    grid-template-columns: 1fr;
  }

  .status-indicators {
    grid-template-columns: 1fr;
  }

  .chart-container {
    height: 160px;
  }
}

@media (max-width: 480px) {
  .detail-card {
    padding: 16px;
  }

  .card-title {
    font-size: 16px;
  }

  .chart-container {
    height: 140px;
  }

  .info-item .label {
    min-width: 60px;
    font-size: 12px;
  }

  .info-item .value {
    font-size: 12px;
    max-width: 150px;
  }
}
</style>
