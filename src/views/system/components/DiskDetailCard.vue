<template>
  <div class="detail-card disk-detail-card">
    <div class="card-header">
      <div class="card-title">
        <el-icon class="card-icon"><Files /></el-icon>
        <span>磁盘详细信息</span>
      </div>
    </div>
    
    <div class="card-content">
      <!-- 磁盘分区信息 -->
      <div class="info-section">
        <h4 class="section-title">磁盘分区</h4>
        <div v-if="diskInfo?.partitions && diskInfo.partitions.length > 0" class="partitions-list">
          <div 
            v-for="(partition, index) in diskInfo.partitions" 
            :key="index" 
            class="partition-item"
          >
            <div class="partition-header">
              <div class="partition-info">
                <span class="device-name">{{ partition.deviceName }}</span>
                <span class="mount-point">{{ partition.mountPoint }}</span>
                <el-tag size="small" type="info">{{ partition.fileSystemType }}</el-tag>
              </div>
              <div class="partition-status">
                <el-tag 
                  size="small" 
                  :type="getDiskStatusType(partition.usagePercent)"
                >
                  {{ partition.usagePercent?.toFixed(1) || 0 }}%
                </el-tag>
              </div>
            </div>
            
            <div class="partition-usage">
              <el-progress 
                :percentage="partition.usagePercent || 0"
                :color="getDiskColor(partition.usagePercent)"
                :stroke-width="8"
                :show-text="false"
              />
            </div>
            
            <div class="partition-details">
              <div class="detail-row">
                <span class="detail-label">总容量:</span>
                <span class="detail-value">{{ partition.totalFormatted || '0 GB' }}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">已使用:</span>
                <span class="detail-value used">{{ partition.usedFormatted || '0 GB' }}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">可用空间:</span>
                <span class="detail-value free">{{ partition.freeFormatted || '0 GB' }}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">权限:</span>
                <div class="permissions">
                  <el-tag v-if="partition.readable" size="small" type="success">可读</el-tag>
                  <el-tag v-if="partition.writable" size="small" type="success">可写</el-tag>
                  <el-tag v-if="!partition.readable && !partition.writable" size="small" type="danger">无权限</el-tag>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="no-partitions">
          <el-empty description="暂无磁盘分区信息" :image-size="60" />
        </div>
      </div>


    </div>
  </div>
</template>

<script setup>
import { Files } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  diskInfo: {
    type: Object,
    default: () => ({})
  }
})



// 获取磁盘状态类型
const getDiskStatusType = (usage) => {
  if (usage >= 90) return 'danger'
  if (usage >= 75) return 'warning'
  return 'success'
}

// 获取磁盘颜色
const getDiskColor = (usage) => {
  if (usage >= 90) return '#f56c6c'
  if (usage >= 75) return '#e6a23c'
  return '#67c23a'
}


</script>

<style scoped>
.detail-card {
  background: var(--el-bg-color);
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--el-border-color-lighter);
  height: fit-content;
}

.card-header {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--el-border-color-extra-light);
}

.card-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.card-icon {
  font-size: 20px;
  color: #909399;
  margin-right: 8px;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.info-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 8px 0;
  padding-bottom: 4px;
  border-bottom: 2px solid var(--el-color-primary);
  display: inline-block;
}

.partitions-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.partition-item {
  padding: 16px;
  background: var(--el-bg-color-page);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-extra-light);
}

.partition-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.partition-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.device-name {
  font-size: 14px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.mount-point {
  font-size: 13px;
  color: var(--el-text-color-regular);
  background: var(--el-bg-color);
  padding: 2px 8px;
  border-radius: 4px;
  border: 1px solid var(--el-border-color-light);
}

.partition-usage {
  margin-bottom: 12px;
}

.partition-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 8px;
  background: var(--el-bg-color);
  border-radius: 4px;
}

.detail-label {
  font-size: 12px;
  color: var(--el-text-color-regular);
}

.detail-value {
  font-size: 12px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.detail-value.used {
  color: #409eff;
}

.detail-value.free {
  color: #67c23a;
}

.permissions {
  display: flex;
  gap: 4px;
}

.no-partitions {
  text-align: center;
  padding: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detail-card {
    padding: 20px;
  }

  .partition-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .partition-info {
    flex-wrap: wrap;
    gap: 8px;
  }

  .partition-details {
    grid-template-columns: 1fr;
  }


}

@media (max-width: 480px) {
  .detail-card {
    padding: 16px;
  }

  .card-title {
    font-size: 16px;
  }

  .partition-item {
    padding: 12px;
  }
}
</style>
