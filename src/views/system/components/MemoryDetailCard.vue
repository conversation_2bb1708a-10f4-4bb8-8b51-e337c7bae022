<template>
  <div class="detail-card memory-detail-card">
    <div class="card-header">
      <div class="card-title">
        <el-icon class="card-icon"><DataBoard /></el-icon>
        <span>内存详细信息</span>
      </div>
    </div>
    
    <div class="card-content">
      <!-- 系统内存 -->
      <div class="info-section">
        <h4 class="section-title">系统内存</h4>
        <div class="memory-chart">
          <div ref="systemMemoryChart" class="chart-container"></div>
        </div>
        <div class="memory-details">
          <div class="memory-item">
            <span class="memory-label">总内存</span>
            <span class="memory-value">{{ memoryInfo?.systemMemory?.totalFormatted || '0 GB' }}</span>
          </div>
          <div class="memory-item">
            <span class="memory-label">已用内存</span>
            <span class="memory-value used">{{ memoryInfo?.systemMemory?.usedFormatted || '0 GB' }}</span>
          </div>
          <div class="memory-item">
            <span class="memory-label">可用内存</span>
            <span class="memory-value free">{{ memoryInfo?.systemMemory?.freeFormatted || '0 GB' }}</span>
          </div>
          <div class="memory-item">
            <span class="memory-label">使用率</span>
            <span class="memory-value" :class="getMemoryStatusClass(memoryInfo?.systemMemory?.usagePercent)">
              {{ memoryInfo?.systemMemory?.usagePercent?.toFixed(1) || 0 }}%
            </span>
          </div>
        </div>
      </div>

      <!-- JVM堆内存 -->
      <div class="info-section">
        <h4 class="section-title">JVM堆内存</h4>
        <div class="memory-chart">
          <div ref="jvmHeapChart" class="chart-container"></div>
        </div>
        <div class="memory-details">
          <div class="memory-item">
            <span class="memory-label">最大内存</span>
            <span class="memory-value">{{ memoryInfo?.jvmMemory?.heapMemory?.maxFormatted || '0 GB' }}</span>
          </div>
          <div class="memory-item">
            <span class="memory-label">已用内存</span>
            <span class="memory-value used">{{ memoryInfo?.jvmMemory?.heapMemory?.usedFormatted || '0 GB' }}</span>
          </div>
          <div class="memory-item">
            <span class="memory-label">可用内存</span>
            <span class="memory-value free">{{ memoryInfo?.jvmMemory?.heapMemory?.freeFormatted || '0 GB' }}</span>
          </div>
          <div class="memory-item">
            <span class="memory-label">使用率</span>
            <span class="memory-value" :class="getMemoryStatusClass(memoryInfo?.jvmMemory?.heapMemory?.usagePercent)">
              {{ memoryInfo?.jvmMemory?.heapMemory?.usagePercent?.toFixed(1) || 0 }}%
            </span>
          </div>
        </div>
      </div>

      <!-- JVM非堆内存 -->
      <div class="info-section">
        <h4 class="section-title">JVM非堆内存</h4>
        <div class="memory-progress">
          <div class="progress-info">
            <span class="progress-label">已用: {{ memoryInfo?.jvmMemory?.nonHeapMemory?.usedFormatted || '0 MB' }}</span>
            <span class="progress-value">{{ memoryInfo?.jvmMemory?.nonHeapMemory?.usagePercent?.toFixed(1) || 0 }}%</span>
          </div>
          <el-progress
            :percentage="memoryInfo?.jvmMemory?.nonHeapMemory?.usagePercent || 0"
            :color="getMemoryColor(memoryInfo?.jvmMemory?.nonHeapMemory?.usagePercent)"
            :stroke-width="12"
            :show-text="false"
          />
          <div class="progress-details">
            <span class="detail-text">最大: {{ memoryInfo?.jvmMemory?.nonHeapMemory?.maxFormatted || '0 MB' }}</span>
          </div>
        </div>
      </div>

      <!-- 交换内存 -->
      <div v-if="memoryInfo?.swapMemory && memoryInfo.swapMemory.totalBytes > 0" class="info-section">
        <h4 class="section-title">交换内存</h4>
        <div class="memory-progress">
          <div class="progress-info">
            <span class="progress-label">已用: {{ memoryInfo?.swapMemory?.usedFormatted || '0 MB' }}</span>
            <span class="progress-value">{{ memoryInfo?.swapMemory?.usagePercent?.toFixed(1) || 0 }}%</span>
          </div>
          <el-progress
            :percentage="memoryInfo?.swapMemory?.usagePercent || 0"
            :color="getMemoryColor(memoryInfo?.swapMemory?.usagePercent)"
            :stroke-width="12"
            :show-text="false"
          />
          <div class="progress-details">
            <span class="detail-text">总计: {{ memoryInfo?.swapMemory?.totalFormatted || '0 MB' }} | 可用: {{ memoryInfo?.swapMemory?.freeFormatted || '0 MB' }}</span>
          </div>
        </div>
      </div>

      <!-- 垃圾回收信息 -->
      <div class="info-section">
        <h4 class="section-title">垃圾回收统计</h4>
        <div class="gc-stats">
          <div class="gc-item">
            <div class="gc-header">
              <span class="gc-label">年轻代GC</span>
              <span class="gc-count">{{ memoryInfo?.jvmMemory?.gcInfo?.youngGcCount || 0 }} 次</span>
            </div>
            <div class="gc-time">总耗时: {{ memoryInfo?.jvmMemory?.gcInfo?.youngGcTime || 0 }} ms</div>
          </div>
          <div class="gc-item">
            <div class="gc-header">
              <span class="gc-label">老年代GC</span>
              <span class="gc-count">{{ memoryInfo?.jvmMemory?.gcInfo?.oldGcCount || 0 }} 次</span>
            </div>
            <div class="gc-time">总耗时: {{ memoryInfo?.jvmMemory?.gcInfo?.oldGcTime || 0 }} ms</div>
          </div>
          <div class="gc-summary">
            <div class="summary-item">
              <span class="summary-label">总GC次数</span>
              <span class="summary-value">{{ memoryInfo?.jvmMemory?.gcInfo?.totalGcCount || 0 }}</span>
            </div>
            <div class="summary-item">
              <span class="summary-label">总GC耗时</span>
              <span class="summary-value">{{ memoryInfo?.jvmMemory?.gcInfo?.totalGcTime || 0 }} ms</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick } from 'vue'
import { DataBoard } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

// Props
const props = defineProps({
  memoryInfo: {
    type: Object,
    default: () => ({})
  }
})

// 图表引用
const systemMemoryChart = ref(null)
const jvmHeapChart = ref(null)
let systemMemoryChartInstance = null
let jvmHeapChartInstance = null

// 获取内存状态类名
const getMemoryStatusClass = (usage) => {
  if (usage >= 85) return 'status-danger'
  if (usage >= 70) return 'status-warning'
  return 'status-normal'
}

// 获取内存颜色
const getMemoryColor = (usage) => {
  if (usage >= 85) return '#f56c6c'
  if (usage >= 70) return '#e6a23c'
  return '#409eff'
}

// 初始化系统内存图表
const initSystemMemoryChart = () => {
  if (!systemMemoryChart.value) return
  
  systemMemoryChartInstance = echarts.init(systemMemoryChart.value)
  
  const systemMemory = props.memoryInfo?.systemMemory || {}
  const usedBytes = systemMemory.usedBytes || 0
  const freeBytes = systemMemory.freeBytes || 0
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [
      {
        name: '系统内存',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '50%'],
        data: [
          { 
            value: usedBytes, 
            name: '已用内存', 
            itemStyle: { color: '#409eff' },
            label: { formatter: systemMemory.usedFormatted || '0 GB' }
          },
          { 
            value: freeBytes, 
            name: '可用内存', 
            itemStyle: { color: '#e8f4fd' },
            label: { formatter: systemMemory.freeFormatted || '0 GB' }
          }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        label: {
          show: true,
          position: 'center',
          fontSize: 12
        }
      }
    ]
  }
  
  systemMemoryChartInstance.setOption(option)
}

// 初始化JVM堆内存图表
const initJvmHeapChart = () => {
  if (!jvmHeapChart.value) return
  
  jvmHeapChartInstance = echarts.init(jvmHeapChart.value)
  
  const heapMemory = props.memoryInfo?.jvmMemory?.heapMemory || {}
  const usedBytes = heapMemory.usedBytes || 0
  const freeBytes = heapMemory.freeBytes || 0
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [
      {
        name: 'JVM堆内存',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '50%'],
        data: [
          { 
            value: usedBytes, 
            name: '已用内存', 
            itemStyle: { color: '#67c23a' },
            label: { formatter: heapMemory.usedFormatted || '0 GB' }
          },
          { 
            value: freeBytes, 
            name: '可用内存', 
            itemStyle: { color: '#f0f9ff' },
            label: { formatter: heapMemory.freeFormatted || '0 GB' }
          }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        label: {
          show: true,
          position: 'center',
          fontSize: 12
        }
      }
    ]
  }
  
  jvmHeapChartInstance.setOption(option)
}

// 更新图表数据
const updateCharts = () => {
  if (systemMemoryChartInstance && props.memoryInfo?.systemMemory) {
    const systemMemory = props.memoryInfo.systemMemory
    const option = {
      series: [
        {
          data: [
            { 
              value: systemMemory.usedBytes || 0, 
              name: '已用内存', 
              itemStyle: { color: '#409eff' }
            },
            { 
              value: systemMemory.freeBytes || 0, 
              name: '可用内存', 
              itemStyle: { color: '#e8f4fd' }
            }
          ]
        }
      ]
    }
    systemMemoryChartInstance.setOption(option)
  }
  
  if (jvmHeapChartInstance && props.memoryInfo?.jvmMemory?.heapMemory) {
    const heapMemory = props.memoryInfo.jvmMemory.heapMemory
    const option = {
      series: [
        {
          data: [
            { 
              value: heapMemory.usedBytes || 0, 
              name: '已用内存', 
              itemStyle: { color: '#67c23a' }
            },
            { 
              value: heapMemory.freeBytes || 0, 
              name: '可用内存', 
              itemStyle: { color: '#f0f9ff' }
            }
          ]
        }
      ]
    }
    jvmHeapChartInstance.setOption(option)
  }
}

// 监听数据变化
watch(() => props.memoryInfo, () => {
  nextTick(() => {
    updateCharts()
  })
}, { deep: true })

// 生命周期
onMounted(() => {
  nextTick(() => {
    initSystemMemoryChart()
    initJvmHeapChart()
  })
})
</script>

<style scoped>
.detail-card {
  background: var(--el-bg-color);
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--el-border-color-lighter);
  height: fit-content;
}

.card-header {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--el-border-color-extra-light);
}

.card-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.card-icon {
  font-size: 20px;
  color: #409eff;
  margin-right: 8px;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.info-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 8px 0;
  padding-bottom: 4px;
  border-bottom: 2px solid var(--el-color-primary);
  display: inline-block;
}

.chart-container {
  height: 180px;
  width: 100%;
}

.memory-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.memory-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: var(--el-bg-color-page);
  border-radius: 6px;
  border: 1px solid var(--el-border-color-extra-light);
}

.memory-label {
  font-size: 13px;
  color: var(--el-text-color-regular);
}

.memory-value {
  font-size: 13px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.memory-value.used {
  color: #409eff;
}

.memory-value.free {
  color: #67c23a;
}

.status-normal {
  color: #67c23a;
}

.status-warning {
  color: #e6a23c;
}

.status-danger {
  color: #f56c6c;
}

.memory-progress {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-label {
  font-size: 13px;
  color: var(--el-text-color-regular);
}

.progress-value {
  font-size: 13px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.progress-details {
  text-align: center;
}

.detail-text {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.gc-stats {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.gc-item {
  padding: 12px;
  background: var(--el-bg-color-page);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-extra-light);
}

.gc-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.gc-label {
  font-size: 14px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.gc-count {
  font-size: 14px;
  font-weight: 600;
  color: #409eff;
}

.gc-time {
  font-size: 12px;
  color: var(--el-text-color-regular);
}

.gc-summary {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  padding: 12px;
  background: var(--el-bg-color-page);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.summary-label {
  font-size: 12px;
  color: var(--el-text-color-regular);
}

.summary-value {
  font-size: 16px;
  font-weight: 700;
  color: var(--el-color-primary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detail-card {
    padding: 20px;
  }

  .memory-details {
    grid-template-columns: 1fr;
  }

  .gc-summary {
    grid-template-columns: 1fr;
  }

  .chart-container {
    height: 160px;
  }
}

@media (max-width: 480px) {
  .detail-card {
    padding: 16px;
  }

  .card-title {
    font-size: 16px;
  }

  .chart-container {
    height: 140px;
  }
}
</style>
