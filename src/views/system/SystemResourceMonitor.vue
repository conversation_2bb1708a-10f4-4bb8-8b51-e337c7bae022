<template>
  <div class="system-resource-monitor">
    <!-- 页面标题和控制区 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">
            <el-icon class="title-icon"><Monitor /></el-icon>
            系统资源监控
          </h1>
          <p class="page-description">实时监控服务器系统资源使用情况</p>
        </div>
        <div class="control-section">
          <el-button 
            type="primary" 
            :icon="Refresh" 
            :loading="loading"
            @click="refreshData"
            size="default"
          >
            刷新数据
          </el-button>
          <el-switch
            v-model="autoRefresh"
            active-text="自动刷新"
            inactive-text="手动刷新"
            @change="handleAutoRefreshChange"
          />
        </div>
      </div>
      
      <!-- 数据采集信息 -->
      <div v-if="resourceData" class="collect-info">
        <el-tag type="info" size="small">
          <el-icon><Clock /></el-icon>
          采集时间: {{ formatTime(resourceData.collectTime) }}
        </el-tag>
        <el-tag type="success" size="small">
          <el-icon><Timer /></el-icon>
          采集耗时: {{ resourceData.collectDuration }}ms
        </el-tag>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading && !resourceData" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <el-result
        icon="error"
        title="数据加载失败"
        :sub-title="error"
      >
        <template #extra>
          <el-button type="primary" @click="refreshData">重新加载</el-button>
        </template>
      </el-result>
    </div>

    <!-- 主要内容 -->
    <div v-else-if="resourceData" class="monitor-content">
      <!-- 系统概览卡片 -->
      <div class="overview-section">
        <div class="overview-cards">
          <!-- CPU使用率卡片 -->
          <div class="overview-card cpu-card">
            <div class="card-header">
              <div class="card-title">
                <el-icon class="card-icon"><Cpu /></el-icon>
                <span>CPU使用率</span>
              </div>
              <div class="card-value" :class="getCpuStatusClass(resourceData.cpuInfo?.usage?.totalPercent)">
                {{ resourceData.cpuInfo?.usage?.totalPercent?.toFixed(1) || 0 }}%
              </div>
            </div>
            <div class="card-content">
              <el-progress 
                :percentage="resourceData.cpuInfo?.usage?.totalPercent || 0"
                :color="getCpuColor(resourceData.cpuInfo?.usage?.totalPercent)"
                :stroke-width="8"
                :show-text="false"
              />
              <div class="cpu-details">
                <div class="detail-item">
                  <span class="label">用户态:</span>
                  <span class="value">{{ resourceData.cpuInfo?.usage?.userPercent?.toFixed(1) || 0 }}%</span>
                </div>
                <div class="detail-item">
                  <span class="label">系统态:</span>
                  <span class="value">{{ resourceData.cpuInfo?.usage?.systemPercent?.toFixed(1) || 0 }}%</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 内存使用率卡片 -->
          <div class="overview-card memory-card">
            <div class="card-header">
              <div class="card-title">
                <el-icon class="card-icon"><DataBoard /></el-icon>
                <span>内存使用率</span>
              </div>
              <div class="card-value" :class="getMemoryStatusClass(resourceData.memoryInfo?.systemMemory?.usagePercent)">
                {{ resourceData.memoryInfo?.systemMemory?.usagePercent?.toFixed(1) || 0 }}%
              </div>
            </div>
            <div class="card-content">
              <el-progress 
                :percentage="resourceData.memoryInfo?.systemMemory?.usagePercent || 0"
                :color="getMemoryColor(resourceData.memoryInfo?.systemMemory?.usagePercent)"
                :stroke-width="8"
                :show-text="false"
              />
              <div class="memory-details">
                <div class="detail-item">
                  <span class="label">已用:</span>
                  <span class="value">{{ resourceData.memoryInfo?.systemMemory?.usedFormatted || '0 GB' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">总计:</span>
                  <span class="value">{{ resourceData.memoryInfo?.systemMemory?.totalFormatted || '0 GB' }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 磁盘使用率卡片 -->
          <div class="overview-card disk-card">
            <div class="card-header">
              <div class="card-title">
                <el-icon class="card-icon"><Files /></el-icon>
                <span>磁盘使用率</span>
              </div>
              <div class="card-value" :class="getDiskStatusClass(getMainDiskUsage())">
                {{ getMainDiskUsage()?.toFixed(1) || 0 }}%
              </div>
            </div>
            <div class="card-content">
              <el-progress 
                :percentage="getMainDiskUsage() || 0"
                :color="getDiskColor(getMainDiskUsage())"
                :stroke-width="8"
                :show-text="false"
              />
              <div class="disk-details">
                <div class="detail-item">
                  <span class="label">已用:</span>
                  <span class="value">{{ getMainDisk()?.usedFormatted || '0 GB' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">总计:</span>
                  <span class="value">{{ getMainDisk()?.totalFormatted || '0 GB' }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 系统负载卡片 -->
          <div class="overview-card load-card">
            <div class="card-header">
              <div class="card-title">
                <el-icon class="card-icon"><DataLine /></el-icon>
                <span>系统负载</span>
              </div>
              <div class="card-value" :class="getLoadStatusClass(resourceData.cpuInfo?.loadAverage?.oneMinute)">
                {{ resourceData.cpuInfo?.loadAverage?.oneMinute?.toFixed(2) || 0 }}
              </div>
            </div>
            <div class="card-content">
              <div class="load-details">
                <div class="detail-item">
                  <span class="label">1分钟:</span>
                  <span class="value">{{ resourceData.cpuInfo?.loadAverage?.oneMinute?.toFixed(2) || 0 }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">5分钟:</span>
                  <span class="value">{{ resourceData.cpuInfo?.loadAverage?.fiveMinutes?.toFixed(2) || 0 }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">15分钟:</span>
                  <span class="value">{{ resourceData.cpuInfo?.loadAverage?.fifteenMinutes?.toFixed(2) || 0 }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 详细监控区域 -->
      <div class="detail-section">
        <el-row :gutter="24">
          <!-- CPU详细信息 -->
          <el-col :xs="24" :sm="24" :md="12" :lg="8">
            <CpuDetailCard :cpu-info="resourceData.cpuInfo" />
          </el-col>
          
          <!-- 内存详细信息 -->
          <el-col :xs="24" :sm="24" :md="12" :lg="8">
            <MemoryDetailCard :memory-info="resourceData.memoryInfo" />
          </el-col>
          
          <!-- JVM信息 -->
          <el-col :xs="24" :sm="24" :md="12" :lg="8">
            <JvmDetailCard :jvm-info="resourceData.jvmInfo" />
          </el-col>
        </el-row>

        <el-row :gutter="24" style="margin-top: 24px;">
          <!-- 磁盘详细信息 -->
          <el-col :span="24">
            <DiskDetailCard :disk-info="resourceData.diskInfo" />
          </el-col>
        </el-row>

        <el-row :gutter="24" style="margin-top: 24px;">
          <!-- 服务器信息 -->
          <el-col :span="24">
            <ServerDetailCard :server-info="resourceData.serverInfo" />
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Monitor, Refresh, Clock, Timer, Cpu, DataBoard,
  Files, DataLine
} from '@element-plus/icons-vue'
import { getSystemResource } from '@/api/system'
import CpuDetailCard from './components/CpuDetailCard.vue'
import MemoryDetailCard from './components/MemoryDetailCard.vue'
import JvmDetailCard from './components/JvmDetailCard.vue'
import DiskDetailCard from './components/DiskDetailCard.vue'
import ServerDetailCard from './components/ServerDetailCard.vue'

// 响应式数据
const loading = ref(false)
const error = ref('')
const resourceData = ref(null)
const autoRefresh = ref(false)
const refreshTimer = ref(null)

// 获取系统资源数据
const fetchResourceData = async () => {
  try {
    loading.value = true
    error.value = ''
    const response = await getSystemResource()
    resourceData.value = response

    if (response.errorMessage) {
      ElMessage.warning(response.errorMessage)
    }
    if (response.warningMessage) {
      ElMessage.warning(response.warningMessage)
    }
  } catch (err) {
    error.value = err.message || '获取系统资源数据失败'
    ElMessage.error(error.value)
  } finally {
    loading.value = false
  }
}

// 刷新数据
const refreshData = () => {
  fetchResourceData()
}

// 处理自动刷新切换
const handleAutoRefreshChange = (enabled) => {
  if (enabled) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
}

// 开始自动刷新
const startAutoRefresh = () => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
  }
  refreshTimer.value = setInterval(() => {
    fetchResourceData()
  }, 30000) // 30秒刷新一次
}

// 停止自动刷新
const stopAutoRefresh = () => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = null
  }
}

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return ''
  return new Date(timeStr).toLocaleString('zh-CN')
}

// 获取主磁盘信息
const getMainDisk = () => {
  return resourceData.value?.diskInfo?.partitions?.[0] || null
}

// 获取主磁盘使用率
const getMainDiskUsage = () => {
  return getMainDisk()?.usagePercent || 0
}

// 状态类名和颜色函数
const getCpuStatusClass = (usage) => {
  if (usage >= 80) return 'status-danger'
  if (usage >= 60) return 'status-warning'
  return 'status-normal'
}

const getCpuColor = (usage) => {
  if (usage >= 80) return '#f56c6c'
  if (usage >= 60) return '#e6a23c'
  return '#67c23a'
}

const getMemoryStatusClass = (usage) => {
  if (usage >= 85) return 'status-danger'
  if (usage >= 70) return 'status-warning'
  return 'status-normal'
}

const getMemoryColor = (usage) => {
  if (usage >= 85) return '#f56c6c'
  if (usage >= 70) return '#e6a23c'
  return '#409eff'
}

const getDiskStatusClass = (usage) => {
  if (usage >= 90) return 'status-danger'
  if (usage >= 75) return 'status-warning'
  return 'status-normal'
}

const getDiskColor = (usage) => {
  if (usage >= 90) return '#f56c6c'
  if (usage >= 75) return '#e6a23c'
  return '#909399'
}

const getLoadStatusClass = (load) => {
  const cores = resourceData.value?.cpuInfo?.logicalCores || 1
  const loadPercent = (load / cores) * 100
  if (loadPercent >= 80) return 'status-danger'
  if (loadPercent >= 60) return 'status-warning'
  return 'status-normal'
}

// 生命周期
onMounted(() => {
  fetchResourceData()
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.system-resource-monitor {
  min-height: 100vh;
  background: var(--el-bg-color-page);
  padding: 20px;
}

/* 页面标题区域 */
.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.title-section {
  flex: 1;
}

.page-title {
  display: flex;
  align-items: center;
  font-size: 28px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 8px 0;
}

.title-icon {
  font-size: 32px;
  color: var(--el-color-primary);
  margin-right: 12px;
}

.page-description {
  font-size: 14px;
  color: var(--el-text-color-regular);
  margin: 0;
}

.control-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.collect-info {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.collect-info .el-tag {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 加载和错误状态 */
.loading-container {
  background: var(--el-bg-color);
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.error-container {
  background: var(--el-bg-color);
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

/* 监控内容 */
.monitor-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 概览卡片区域 */
.overview-section {
  width: 100%;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.overview-card {
  background: var(--el-bg-color);
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--el-border-color-lighter);
  transition: all 0.3s ease;
}

.overview-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.card-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.card-icon {
  font-size: 20px;
  margin-right: 8px;
}

.cpu-card .card-icon {
  color: #67c23a;
}

.memory-card .card-icon {
  color: #409eff;
}

.disk-card .card-icon {
  color: #909399;
}

.load-card .card-icon {
  color: #e6a23c;
}

.card-value {
  font-size: 24px;
  font-weight: 700;
  transition: color 0.3s ease;
}

.status-normal {
  color: #67c23a;
}

.status-warning {
  color: #e6a23c;
}

.status-danger {
  color: #f56c6c;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.cpu-details,
.memory-details,
.disk-details,
.load-details {
  display: flex;
  justify-content: space-between;
  gap: 16px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.detail-item .label {
  font-size: 12px;
  color: var(--el-text-color-regular);
  margin-bottom: 4px;
}

.detail-item .value {
  font-size: 14px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

/* 详细监控区域 */
.detail-section {
  width: 100%;
}



/* 响应式设计 */
@media (max-width: 1200px) {
  .overview-cards {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}

@media (max-width: 768px) {
  .system-resource-monitor {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .control-section {
    width: 100%;
    justify-content: space-between;
  }

  .page-title {
    font-size: 24px;
  }

  .title-icon {
    font-size: 28px;
  }

  .overview-cards {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .overview-card {
    padding: 20px;
  }

  .card-value {
    font-size: 20px;
  }

  .cpu-details,
  .memory-details,
  .disk-details {
    gap: 12px;
  }

  .load-details {
    flex-direction: column;
    gap: 8px;
  }

  .load-details .detail-item {
    flex-direction: row;
    justify-content: space-between;
  }

  .detail-section .el-row {
    margin-bottom: 20px;
  }
  .detail-section .el-row {
    gap: 24px;
  }
}

@media (max-width: 480px) {
  .system-resource-monitor {
    padding: 12px;
  }


  .page-title {
    font-size: 20px;
  }

  .title-icon {
    font-size: 24px;
  }

  .overview-card {
    padding: 16px;
  }

  .card-title {
    font-size: 14px;
  }

  .card-icon {
    font-size: 18px;
  }

  .card-value {
    font-size: 18px;
  }

  .cpu-details,
  .memory-details,
  .disk-details {
    flex-direction: column;
    gap: 8px;
  }

  .cpu-details .detail-item,
  .memory-details .detail-item,
  .disk-details .detail-item {
    flex-direction: row;
    justify-content: space-between;
  }

  .detail-section .el-row {
    margin-bottom: 16px;
  }
}
</style>
