<template>
  <div class="system-preferences">
    <!-- 主题设置 -->
    <div class="preference-section">
      <div class="section-header">
        <el-icon class="section-icon"><Sunny /></el-icon>
        <h3 class="section-title">主题设置</h3>
      </div>
      <div class="section-content">
        <div class="preference-item">
          <div class="item-info">
            <div class="item-title">外观主题</div>
            <div class="item-description">选择您偏好的界面主题风格</div>
          </div>
          <div class="item-control">
            <el-radio-group v-model="currentTheme" @change="handleThemeChange">
              <el-radio value="light">
                <el-icon><Sunny /></el-icon>
                浅色主题
              </el-radio>
              <el-radio value="dark">
                <el-icon><Moon /></el-icon>
                深色主题
              </el-radio>
            </el-radio-group>
          </div>
        </div>
        
        <div class="preference-item">
          <div class="item-info">
            <div class="item-title">跟随系统</div>
            <div class="item-description">自动跟随系统的主题设置</div>
          </div>
          <div class="item-control">
            <el-switch 
              v-model="followSystem" 
              @change="handleFollowSystemChange"
              active-text="开启"
              inactive-text="关闭"
            />
          </div>
        </div>
      </div>
    </div>


  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Sunny, Moon } from '@element-plus/icons-vue'
import { useThemeStore } from '@/stores/theme'

// Store
const themeStore = useThemeStore()

// 响应式数据
const followSystem = ref(false)

// 计算属性
const currentTheme = computed({
  get: () => themeStore.currentTheme,
  set: (value) => {
    themeStore.setTheme(value)
  }
})

// 方法
const handleThemeChange = (theme) => {
  themeStore.setTheme(theme)
  followSystem.value = false
  ElMessage.success(`已切换到${theme === 'dark' ? '深色' : '浅色'}主题`)
}

const handleFollowSystemChange = (follow) => {
  if (follow) {
    themeStore.resetToSystem()
    ElMessage.success('已开启跟随系统主题')
  } else {
    ElMessage.info('已关闭跟随系统主题')
  }
}



// 初始化
onMounted(() => {
  // 检查是否跟随系统主题
  try {
    const savedTheme = localStorage.getItem('app_theme')
    followSystem.value = !savedTheme
  } catch (error) {
    console.warn('Failed to check theme setting:', error)
  }
})
</script>

<style scoped>
.system-preferences {
  max-width: 800px;
}

.preference-section {
  margin-bottom: 32px;
}

.preference-section:last-child {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.section-icon {
  font-size: 18px;
  color: var(--el-color-primary);
  margin-right: 8px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0;
}

.section-content {
  padding-left: 26px;
}

.preference-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 0;
  border-bottom: 1px solid var(--el-border-color-extra-light);
}

.preference-item:last-child {
  border-bottom: none;
}

.item-info {
  flex: 1;
}

.item-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.item-description {
  font-size: 12px;
  color: var(--el-text-color-regular);
  line-height: 1.4;
}

.item-control {
  flex-shrink: 0;
  margin-left: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .preference-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .item-control {
    margin-left: 0;
    width: 100%;
  }
  
  .section-content {
    padding-left: 0;
  }
}
</style>
