<template>
  <div class="profile-center">
    <div class="profile-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">个人中心</h1>
        <p class="page-description">管理您的个人信息、安全设置和系统偏好</p>
      </div>

      <!-- Tab标签页 -->
      <div class="profile-tabs-container">
        <el-tabs
          v-model="activeTab"
          type="border-card"
          class="profile-tabs"
          @tab-change="handleTabChange"
        >
          <!-- 基本信息Tab -->
          <el-tab-pane name="basic" class="tab-pane">
            <template #label>
              <div class="tab-label">
                <el-icon><User /></el-icon>
                <span>基本信息</span>
              </div>
            </template>
            <div class="tab-content">
              <UserInfoSection />
            </div>
          </el-tab-pane>

          <!-- 修改密码Tab -->
          <el-tab-pane name="password" class="tab-pane">
            <template #label>
              <div class="tab-label">
                <el-icon><Key /></el-icon>
                <span>修改密码</span>
              </div>
            </template>
            <div class="tab-content">
              <div class="password-section">
                <div class="section-header">
                  <h3 class="section-title">修改登录密码</h3>
                  <p class="section-description">为了您的账户安全，请定期更换密码</p>
                </div>
                <PasswordManagement />
              </div>
            </div>
          </el-tab-pane>

          <!-- 安全设置Tab -->
          <el-tab-pane name="security" class="tab-pane">
            <template #label>
              <div class="tab-label">
                <el-icon><Lock /></el-icon>
                <span>安全设置</span>
              </div>
            </template>
            <div class="tab-content">
              <div class="security-sections">
                <!-- 谷歌验证器 -->
                <div class="security-section">
                  <div class="section-header">
                    <h3 class="section-title">双因子认证</h3>
                    <p class="section-description">使用谷歌验证器增强账户安全</p>
                  </div>
                  <GoogleAuthManagement />
                </div>
              </div>
            </div>
          </el-tab-pane>

          <!-- 系统偏好Tab -->
          <el-tab-pane name="preferences" class="tab-pane">
            <template #label>
              <div class="tab-label">
                <el-icon><Setting /></el-icon>
                <span>系统偏好</span>
              </div>
            </template>
            <div class="tab-content">
              <SystemPreferences />
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { User, Key, Lock, Setting } from '@element-plus/icons-vue'
import UserInfoSection from './components/UserInfoSection.vue'
import PasswordManagement from './components/PasswordManagement.vue'
import GoogleAuthManagement from './components/GoogleAuthManagement.vue'
import SystemPreferences from './components/SystemPreferences.vue'

// 路由相关
const route = useRoute()
const router = useRouter()

// 响应式数据
const activeTab = ref('basic')

// 方法
const handleTabChange = (tabName) => {
  console.log('切换到Tab:', tabName)
  // 更新URL查询参数，但不触发页面刷新
  router.replace({
    path: route.path,
    query: { ...route.query, tab: tabName }
  })
}

// 根据URL查询参数设置激活的Tab
const initActiveTab = () => {
  const tabFromQuery = route.query.tab
  if (tabFromQuery && ['basic', 'password', 'security', 'preferences'].includes(tabFromQuery)) {
    activeTab.value = tabFromQuery
    console.log('从URL参数激活Tab:', tabFromQuery)
  }
}

// 监听路由查询参数变化
watch(
  () => route.query.tab,
  (newTab) => {
    if (newTab && ['basic', 'password', 'security', 'preferences'].includes(newTab)) {
      activeTab.value = newTab
      console.log('路由参数变化，切换到Tab:', newTab)
    }
  }
)

// 初始化
onMounted(() => {
  initActiveTab()
})
</script>

<style scoped>
.profile-center {
  min-height: 100vh;
  background: var(--el-bg-color-page);
  padding: 20px;
}

.profile-container {
  max-width: 1200px;
  margin: 0 auto;
}

/* 页面标题 */
.page-header {
  margin-bottom: 30px;
  text-align: center;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 8px 0;
}

.page-description {
  font-size: 14px;
  color: var(--el-text-color-regular);
  margin: 0;
}

/* Tab容器 */
.profile-tabs-container {
  background: var(--el-bg-color);
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Tab标签页样式 */
.profile-tabs {
  border: none;
}

.profile-tabs :deep(.el-tabs__header) {
  margin: 0;
  background: var(--el-bg-color-page);
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.profile-tabs :deep(.el-tabs__nav-wrap) {
  padding: 0 20px;
}

.profile-tabs :deep(.el-tabs__item) {
  height: 60px;
  line-height: 60px;
  padding: 0 24px;
  font-weight: 500;
  border: none;
  border-radius: 0;
  transition: all 0.3s ease;
}

.profile-tabs :deep(.el-tabs__item:hover) {
  color: var(--el-color-primary);
}

.profile-tabs :deep(.el-tabs__item.is-active) {
  color: var(--el-color-primary);
  background: var(--el-bg-color);
  border-bottom: 2px solid var(--el-color-primary);
}

.profile-tabs :deep(.el-tabs__content) {
  padding: 0;
}

/* Tab标签内容 */
.tab-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.tab-label .el-icon {
  font-size: 16px;
}

/* Tab面板内容 */
.tab-pane {
  min-height: 500px;
}

.tab-content {
  padding: 32px;
}

/* 密码设置部分 */
.password-section {
  position: relative;
}

/* 安全设置部分 */
.security-sections {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.security-section {
  position: relative;
}

.security-section:not(:last-child)::after {
  content: '';
  position: absolute;
  bottom: -20px;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--el-border-color-extra-light);
}

.section-header {
  margin-bottom: 20px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 4px 0;
}

.section-description {
  font-size: 13px;
  color: var(--el-text-color-regular);
  margin: 0;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .profile-container {
    max-width: 100%;
  }
}

/* 平板适配 */
@media (max-width: 768px) {
  .profile-center {
    padding: 16px;
  }

  .page-header {
    margin-bottom: 20px;
  }

  .page-title {
    font-size: 24px;
  }

  .profile-tabs :deep(.el-tabs__nav-wrap) {
    padding: 0 16px;
  }

  .profile-tabs :deep(.el-tabs__item) {
    height: 50px;
    line-height: 50px;
    padding: 0 16px;
    font-size: 13px;
  }

  .tab-content {
    padding: 24px 20px;
  }

  .tab-label {
    gap: 6px;
    font-size: 13px;
  }

  .tab-label .el-icon {
    font-size: 14px;
  }

  .security-sections {
    gap: 32px;
  }
}

/* 手机端适配 */
@media (max-width: 480px) {
  .profile-center {
    padding: 12px;
  }

  .page-title {
    font-size: 20px;
  }

  .page-description {
    font-size: 13px;
  }

  .profile-tabs-container {
    border-radius: 6px;
  }

  .profile-tabs :deep(.el-tabs__nav-wrap) {
    padding: 0 12px;
  }

  .profile-tabs :deep(.el-tabs__item) {
    height: 45px;
    line-height: 45px;
    padding: 0 12px;
    font-size: 12px;
  }

  .tab-content {
    padding: 20px 16px;
  }

  .tab-label {
    gap: 4px;
    font-size: 12px;
  }

  .tab-label .el-icon {
    font-size: 13px;
  }

  .security-sections {
    gap: 28px;
  }

  .section-title {
    font-size: 15px;
  }

  .section-description {
    font-size: 12px;
  }
}
</style>
