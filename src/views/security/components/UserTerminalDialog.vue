<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`${userInfo.username} - 终端信息`"
    :width="dialogWidth"
    :close-on-click-modal="false"
    append-to-body
    class="terminal-dialog"
    @close="handleDialogClose"
  >
    <div class="dialog-content">
      <el-table
        v-loading="loading"
        :data="terminals"
        style="width: 100%"
        :header-cell-style="{ background: 'var(--el-fill-color-lighter)', color: 'var(--el-text-color-primary)', fontWeight: '600' }"
        :row-style="{ height: '48px' }"
        :size="isMobile ? 'small' : 'default'"
        table-layout="fixed"
      >
        <el-table-column prop="index" label="序号" width="60" align="center" header-align="center" />

        <el-table-column prop="deviceType" label="设备类型" width="110" align="center" header-align="center">
          <template #default="{ row }">
            <el-tag :type="getDeviceTypeTagType(row.deviceType)" size="small">
              {{ getDeviceTypeText(row.deviceType) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="clientIp" label="IP地址" width="140" align="center" header-align="center" />

        <el-table-column
          v-if="!isMobile"
          prop="location"
          label="位置"
          width="120"
          align="center"
          header-align="center"
          show-overflow-tooltip
        />

        <el-table-column prop="loginTime" label="登录时间" min-width="160" align="center" header-align="center">
          <template #default="{ row }">
            <span :title="row.loginTime">
              {{ isMobile ? formatMobileTime(row.loginTime) : row.loginTime }}
            </span>
          </template>
        </el-table-column>

        <el-table-column prop="onlineDuration" label="在线时长" width="100" align="center" header-align="center">
          <template #default="{ row }">
            {{ formatDuration(row.onlineDuration) }}
          </template>
        </el-table-column>

        <el-table-column prop="isCurrent" label="当前终端" width="90" align="center" header-align="center">
          <template #default="{ row }">
            <el-tag :type="row.isCurrent ? 'success' : 'info'" size="small">
              {{ row.isCurrent ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="140" align="center" header-align="center">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-tooltip content="踢出此终端" placement="top">
                <el-button
                  type="warning"
                  size="small"
                  @click="handleKickoutTerminal(row)"
                  :disabled="row.isCurrent"
                  :icon="Close"
                  circle
                />
              </el-tooltip>
              <el-tooltip content="根据Token踢出" placement="top">
                <el-button
                  type="danger"
                  size="small"
                  @click="handleKickoutByToken(row)"
                  :icon="Delete"
                  circle
                />
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>
    
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false" :size="isMobile ? 'default' : 'default'">
          关闭
        </el-button>
        <el-button 
          type="primary" 
          @click="handleRefresh" 
          :loading="loading"
          :size="isMobile ? 'default' : 'default'"
        >
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh,
  Close,
  Delete
} from '@element-plus/icons-vue'
import {
  kickoutUser,
  kickoutUserByTokenMd5
} from '@/api/onlineUser'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  userInfo: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:visible', 'refresh'])

// 响应式数据
const loading = ref(false)
const terminals = ref([])
const isMobile = ref(false)

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const dialogWidth = computed(() => {
  if (isMobile.value) {
    return '95%'
  }
  return '1100px'
})

// 获取设备类型文本
const getDeviceTypeText = (type) => {
  const commonTypeMap = {
    'PC': '桌面端',
    'WEB': '网页端',
    'APP': '移动应用',
    'MOBILE': '移动端',
    'HD': '高清设备',
    'TABLET': '平板设备'
  }
  return commonTypeMap[type] || type
}

// 获取设备类型标签类型
const getDeviceTypeTagType = (type) => {
  const commonTypeMap = {
    'PC': 'primary',
    'WEB': 'success',
    'APP': 'warning',
    'MOBILE': 'info',
    'HD': 'danger',
    'TABLET': 'warning'
  }
  return commonTypeMap[type] || 'info'
}

// 格式化时长
const formatDuration = (seconds) => {
  if (!seconds || seconds < 0) return '0分钟'
  
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  
  if (isMobile.value) {
    if (hours > 0) {
      return `${hours}h${minutes}m`
    } else {
      return `${minutes}m`
    }
  } else {
    if (hours > 0) {
      return `${hours}小时${minutes}分钟`
    } else {
      return `${minutes}分钟`
    }
  }
}

// 格式化移动端时间显示
const formatMobileTime = (timeStr) => {
  if (!timeStr) return ''
  // 只显示月-日 时:分
  const date = new Date(timeStr)
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hour = String(date.getHours()).padStart(2, '0')
  const minute = String(date.getMinutes()).padStart(2, '0')
  return `${month}-${day} ${hour}:${minute}`
}

// 获取终端信息（直接使用props中的数据）
const fetchTerminals = () => {
  terminals.value = props.userInfo.terminals || []
}

// 踢出指定终端
const handleKickoutTerminal = async (terminal) => {
  try {
    await ElMessageBox.confirm(
      `确定要踢出用户"${props.userInfo.username}"在${getDeviceTypeText(terminal.deviceType)}上的会话吗？`,
      '踢出终端确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await kickoutUser(props.userInfo.loginId, terminal.deviceType)
    ElMessage.success('踢出终端成功')
    fetchTerminals()
    emit('refresh')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('踢出终端失败:', error)
      ElMessage.error('踢出终端失败')
    }
  }
}

// 根据Token MD5踢出
const handleKickoutByToken = async (terminal) => {
  try {
    await ElMessageBox.confirm(
      `确定要根据Token踢出此终端吗？`,
      '根据Token踢出确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await kickoutUserByTokenMd5(terminal.tokenMd5)
    ElMessage.success('踢出成功')
    fetchTerminals()
    emit('refresh')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('踢出失败:', error)
      ElMessage.error('踢出失败')
    }
  }
}

// 刷新终端信息
const handleRefresh = () => {
  fetchTerminals()
  // 通知父组件刷新在线用户列表数据
  emit('refresh')
  ElMessage.success('终端信息已刷新')
}

// 对话框关闭处理
const handleDialogClose = () => {
  terminals.value = []
}

// 检查是否为移动端
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

// 监听对话框显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    checkMobile()
    fetchTerminals()
  }
})

// 监听窗口大小变化
window.addEventListener('resize', checkMobile)
</script>

<style scoped>
.dialog-content {
  padding: 0;
}

.empty-state {
  padding: 40px 0;
  text-align: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.action-buttons {
  display: flex;
  gap: 6px;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  margin: 0;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-buttons .el-button.is-circle {
  padding: 0;
  transition: all 0.2s ease;
}

.action-buttons .el-button:hover {
  transform: scale(1.05);
}

.action-buttons .el-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.action-buttons .el-button:disabled:hover {
  transform: none;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .dialog-footer {
    flex-direction: column-reverse;
    gap: 8px;
  }
  
  .dialog-footer .el-button {
    width: 100%;
  }
  
  .action-buttons {
    gap: 4px;
  }

  .action-buttons .el-button {
    width: 26px;
    height: 26px;
  }
}
</style>

<style>
/* 对话框整体样式优化 */
.terminal-dialog .el-dialog {
  margin: 5vh auto 50px;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.terminal-dialog .el-dialog__header {
  padding: 20px 20px 0 20px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.terminal-dialog .el-dialog__title {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.terminal-dialog .el-dialog__body {
  padding: 20px 20px 0 20px;
  max-height: 70vh;
  overflow-y: auto;
}

.terminal-dialog .el-table {
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 6px;
}

.terminal-dialog .el-table .el-table__header-wrapper {
  border-radius: 6px 6px 0 0;
}

.terminal-dialog .el-dialog__footer {
  padding: 20px 20px 20px 20px;
  border-top: 1px solid var(--el-border-color-lighter);
}

/* 移动端对话框适配 */
@media (max-width: 768px) {
  .terminal-dialog .el-dialog {
    margin: 2vh auto 20px;
    width: 95% !important;
    max-width: none;
  }

  .terminal-dialog .el-dialog__header {
    padding: 16px 16px 0 16px;
  }

  .terminal-dialog .el-dialog__body {
    padding: 16px;
    max-height: 60vh;
  }

  .terminal-dialog .el-dialog__footer {
    padding: 0 16px 16px 16px;
  }
  
  .terminal-dialog .el-table {
    font-size: 12px;
  }
  
  .terminal-dialog .el-table .el-table__cell {
    padding: 8px 4px;
  }
}
</style>
