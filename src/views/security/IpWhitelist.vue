<template>
  <div class="management-page">
    <!-- 搜索和操作区域 -->
    <div class="search-section">
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="白名单类型">
          <el-select
            v-model="searchForm.whitelistType"
            placeholder="请选择类型"
            clearable
            class="search-select"
          >
            <el-option
              v-for="item in whitelistTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="IP地址">
          <el-input
            v-model="searchForm.ipAddress"
            placeholder="请输入IP地址"
            clearable
            class="search-input"
            @keyup.enter="handleSearch"
          />
        </el-form-item>

        <el-form-item label="用户名">
          <el-input
            v-model="searchForm.username"
            placeholder="请输入用户名"
            clearable
            class="search-input"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.enableStatus"
            placeholder="请选择状态"
            clearable
            class="search-select"
          >
            <el-option
              v-for="item in enableStatusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>

      <div class="action-buttons">
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          新增白名单
        </el-button>
        <el-button
          type="success"
          :disabled="!hasSelection"
          @click="handleBatchEnable"
        >
          <el-icon><Check /></el-icon>
          批量启用
        </el-button>
        <el-button
          type="warning"
          :disabled="!hasSelection"
          @click="handleBatchDisable"
        >
          <el-icon><Close /></el-icon>
          批量禁用
        </el-button>
        <el-button
          type="danger"
          :disabled="!hasSelection"
          @click="handleBatchDelete"
        >
          <el-icon><Delete /></el-icon>
          批量删除
        </el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-table
        v-loading="loading"
        :data="tableData"
        @selection-change="handleSelectionChange"
        style="width: 100%"
        :header-cell-style="{ background: 'var(--bg-color-table-header)', color: 'var(--text-color-primary)', fontWeight: '600' }"
        :row-style="{ height: '56px' }"
      >
        <el-table-column type="selection" width="55" />

        <el-table-column prop="whitelistType" label="类型" width="120" align="center" header-align="center">
          <template #default="{ row }">
            <el-tag :type="row.whitelistType === 1 ? 'primary' : 'success'" size="small">
              {{ getWhitelistTypeText(row.whitelistType) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="ipAddress" label="IP地址" min-width="150" align="center" header-align="center" />

        <el-table-column prop="username" label="用户名" width="120" align="center" header-align="center" />

        <el-table-column prop="enableStatus" label="状态" width="80" align="center" header-align="center">
          <template #default="{ row }">
            <el-tag :type="row.enableStatus === 1 ? 'success' : 'danger'" size="small">
              {{ getEnableStatusText(row.enableStatus) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="remark" label="备注" min-width="150" show-overflow-tooltip align="center" header-align="center" />

        <el-table-column prop="createTime" label="创建时间" width="160" align="center" header-align="center" />

        <el-table-column label="操作" width="220" fixed="right" align="center" header-align="center">
          <template #default="{ row }">
            <div class="action-buttons-row">
              <el-tooltip content="编辑" placement="top">
                <el-button
                  type="primary"
                  size="small"
                  @click="handleEdit(row)"
                  :icon="Edit"
                  circle
                />
              </el-tooltip>
              <el-tooltip :content="row.enableStatus === 1 ? '禁用' : '启用'" placement="top">
                <el-button
                  :type="row.enableStatus === 1 ? 'warning' : 'success'"
                  size="small"
                  @click="handleToggleStatus(row)"
                  :icon="row.enableStatus === 1 ? Close : Check"
                  circle
                />
              </el-tooltip>
              <el-tooltip content="删除" placement="top">
                <el-button
                  type="danger"
                  size="small"
                  @click="handleDelete(row)"
                  :icon="Delete"
                  circle
                />
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <Pagination
        v-model:current="pagination.current"
        v-model:pageSize="pagination.size"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 添加/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      :width="dialogWidth"
      :close-on-click-modal="false"
      append-to-body
      class="ip-whitelist-dialog"
      @close="handleDialogClose"
    >
      <div class="dialog-content">
        <el-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          :label-width="labelWidth"
          :label-position="labelPosition"
          @submit.prevent
        >
        <el-form-item label="白名单类型" prop="whitelistType">
          <el-select v-model="formData.whitelistType" placeholder="请选择类型" style="width: 100%">
            <el-option
              v-for="item in whitelistTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="IP地址" prop="ipAddress">
          <el-input
            v-model="formData.ipAddress"
            placeholder="请输入IP地址或IP段（支持CIDR格式）"
          />
          <div class="form-tip">
            支持单个IP（如：*************）或IP段（如：***********/24）
          </div>
        </el-form-item>
        
        <el-form-item label="启用状态" prop="enableStatus">
          <el-radio-group v-model="formData.enableStatus">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息（可选）"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Plus,
  Check,
  Close,
  Delete,
  Edit
} from '@element-plus/icons-vue'
import {
  getIpWhitelistPage,
  addIpWhitelist,
  updateIpWhitelist,
  deleteIpWhitelist,
  batchDeleteIpWhitelist,
  batchUpdateIpWhitelistStatus,
  whitelistTypeOptions,
  enableStatusOptions,
  WhitelistTypeEnum,
  EnableStatusEnum
} from '@/api/ipWhitelist'

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const dialogVisible = ref(false)
const tableData = ref([])
const selectedItems = ref([])
const isMobile = ref(false)

// 搜索表单
const searchForm = reactive({
  whitelistType: null,
  ipAddress: '',
  username: '',
  enableStatus: null
})

// 分页信息
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 表单数据
const formData = reactive({
  id: '',
  whitelistType: WhitelistTypeEnum.SYSTEM,
  ipAddress: '',
  enableStatus: EnableStatusEnum.ENABLED,
  remark: ''
})

// 表单引用
const formRef = ref()

// 计算属性
const dialogTitle = computed(() => {
  return formData.id ? '编辑IP白名单' : '新增IP白名单'
})

const hasSelection = computed(() => {
  return selectedItems.value.length > 0
})

// 检查是否为移动端
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

// 移动端适配计算属性
const dialogWidth = computed(() => {
  return isMobile.value ? '90%' : '600px'
})

const labelWidth = computed(() => {
  return isMobile.value ? '80px' : '100px'
})

const labelPosition = computed(() => {
  return isMobile.value ? 'top' : 'right'
})

// IP地址验证规则
const validateIpAddress = (_rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入IP地址'))
    return
  }

  // 单个IP地址正则
  const ipRegex = /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/

  // CIDR格式正则
  const cidrRegex = /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\/([0-9]|[1-2][0-9]|3[0-2])$/

  if (!ipRegex.test(value) && !cidrRegex.test(value)) {
    callback(new Error('请输入正确的IP地址或CIDR格式的IP段'))
    return
  }

  callback()
}

// 表单验证规则
const formRules = {
  whitelistType: [
    { required: true, message: '请选择白名单类型', trigger: 'change' }
  ],
  ipAddress: [
    { required: true, validator: validateIpAddress, trigger: 'blur' }
  ],
  enableStatus: [
    { required: true, message: '请选择启用状态', trigger: 'change' }
  ],
  remark: [
    { max: 500, message: '备注不能超过500个字符', trigger: 'blur' }
  ]
}

// 获取白名单类型文本
const getWhitelistTypeText = (type) => {
  const option = whitelistTypeOptions.find(item => item.value === type)
  return option ? option.label : '未知'
}

// 获取启用状态文本
const getEnableStatusText = (status) => {
  const option = enableStatusOptions.find(item => item.value === status)
  return option ? option.label : '未知'
}

// 查询数据
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      current: pagination.current,
      size: pagination.size,
      ...searchForm
    }
    const response = await getIpWhitelistPage(params)
    tableData.value = response.records || []
    pagination.total = response.total || 0
  } catch (error) {
    console.error('获取IP白名单列表失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 处理搜索
const handleSearch = () => {
  pagination.current = 1
  fetchData()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    whitelistType: null,
    ipAddress: '',
    username: '',
    enableStatus: null
  })
  pagination.current = 1
  fetchData()
}

// 处理选择变化
const handleSelectionChange = (selection) => {
  selectedItems.value = selection
}

// 分页处理
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1
  fetchData()
}

const handleCurrentChange = (current) => {
  pagination.current = current
  fetchData()
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    id: '',
    whitelistType: WhitelistTypeEnum.SYSTEM,
    ipAddress: '',
    enableStatus: EnableStatusEnum.ENABLED,
    remark: ''
  })
  formRef.value?.clearValidate()
}

// 处理新增
const handleAdd = () => {
  resetForm()
  dialogVisible.value = true
}

// 处理编辑
const handleEdit = (row) => {
  resetForm()
  Object.assign(formData, {
    id: row.id,
    whitelistType: row.whitelistType,
    ipAddress: row.ipAddress,
    enableStatus: row.enableStatus,
    remark: row.remark || ''
  })
  dialogVisible.value = true
}

// 处理提交
const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    submitLoading.value = true

    if (formData.id) {
      // 更新
      await updateIpWhitelist(formData)
      ElMessage.success('更新成功')
    } else {
      // 新增
      await addIpWhitelist(formData)
      ElMessage.success('添加成功')
    }

    dialogVisible.value = false
    fetchData()
  } catch (error) {
    if (error.message) {
      ElMessage.error(error.message)
    }
  } finally {
    submitLoading.value = false
  }
}

// 处理删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除IP地址"${row.ipAddress}"的白名单记录吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteIpWhitelist(row.id)
    ElMessage.success('删除成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 处理状态切换
const handleToggleStatus = async (row) => {
  const newStatus = row.enableStatus === 1 ? 0 : 1
  const statusText = newStatus === 1 ? '启用' : '禁用'

  try {
    await ElMessageBox.confirm(
      `确定要${statusText}IP地址"${row.ipAddress}"的白名单记录吗？`,
      '状态变更确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await batchUpdateIpWhitelistStatus([row.id], newStatus)
    ElMessage.success(`${statusText}成功`)
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(`${statusText}失败`)
    }
  }
}

// 处理批量启用
const handleBatchEnable = async () => {
  const ids = selectedItems.value.map(item => item.id)

  try {
    await ElMessageBox.confirm(
      `确定要启用选中的${ids.length}条白名单记录吗？`,
      '批量启用确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await batchUpdateIpWhitelistStatus(ids, EnableStatusEnum.ENABLED)
    ElMessage.success('批量启用成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量启用失败')
    }
  }
}

// 处理批量禁用
const handleBatchDisable = async () => {
  const ids = selectedItems.value.map(item => item.id)

  try {
    await ElMessageBox.confirm(
      `确定要禁用选中的${ids.length}条白名单记录吗？`,
      '批量禁用确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await batchUpdateIpWhitelistStatus(ids, EnableStatusEnum.DISABLED)
    ElMessage.success('批量禁用成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量禁用失败')
    }
  }
}

// 处理批量删除
const handleBatchDelete = async () => {
  const ids = selectedItems.value.map(item => item.id)

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的${ids.length}条白名单记录吗？此操作不可恢复！`,
      '批量删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await batchDeleteIpWhitelist(ids)
    ElMessage.success('批量删除成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

// 对话框关闭处理
const handleDialogClose = () => {
  resetForm()
}

// 生命周期
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
  fetchData()
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<style scoped>
/* 使用全局统一的管理页面样式 */

.form-tip {
  font-size: 12px;
  color: var(--el-text-color-placeholder);
  margin-top: 4px;
  line-height: 1.4;
}

.dialog-footer {
  text-align: right;
}

/* 搜索表单控件样式 */
.search-input {
  width: 200px;
}

.search-select {
  width: 120px;
}

/* 表格操作按钮 */
.action-buttons-row {
  display: flex;
  gap: 4px;
  align-items: center;
  justify-content: center;
}

/* 对话框内容区域 */
.dialog-content {
  padding: 20px 0;
}

.form-tip {
  font-size: 12px;
  color: var(--el-text-color-placeholder);
  margin-top: 4px;
  line-height: 1.4;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .dialog-content {
    padding: 16px 0;
  }

  .dialog-footer {
    padding-top: 16px;
    gap: 8px;
  }

  .dialog-footer .el-button {
    flex: 1;
    min-width: 80px;
  }
}

/* 超小屏幕适配 */
@media (max-width: 480px) {
  .dialog-content {
    padding: 12px 0;
  }

  .dialog-footer {
    flex-direction: column-reverse;
    gap: 12px;
    padding-top: 16px;
  }

  .dialog-footer .el-button {
    width: 100%;
    margin: 0;
  }
}
</style>

<!-- 全局样式 -->
<style>
/* 对话框整体样式优化 */
.ip-whitelist-dialog .el-dialog {
  margin: 15vh auto 50px;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.ip-whitelist-dialog .el-dialog__header {
  padding: 20px 20px 0 20px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.ip-whitelist-dialog .el-dialog__title {
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.ip-whitelist-dialog .el-dialog__body {
  padding: 0 20px;
}

.ip-whitelist-dialog .el-dialog__footer {
  padding: 0 20px 20px 20px;
}

/* 移动端对话框适配 */
@media (max-width: 768px) {
  .ip-whitelist-dialog .el-dialog {
    margin: 10vh auto 20px;
    width: 90% !important;
    max-width: none;
  }

  .ip-whitelist-dialog .el-dialog__header {
    padding: 16px 16px 0 16px;
  }

  .ip-whitelist-dialog .el-dialog__body {
    padding: 0 16px;
  }

  .ip-whitelist-dialog .el-dialog__footer {
    padding: 0 16px 16px 16px;
  }

  .ip-whitelist-dialog .el-form-item {
    margin-bottom: 16px;
  }
}

/* 超小屏幕对话框适配 */
@media (max-width: 480px) {
  .ip-whitelist-dialog .el-dialog {
    margin: 5vh auto 10px;
    width: 95% !important;
  }

  .ip-whitelist-dialog .el-dialog__header {
    padding: 12px 12px 0 12px;
  }

  .ip-whitelist-dialog .el-dialog__body {
    padding: 0 12px;
  }

  .ip-whitelist-dialog .el-dialog__footer {
    padding: 0 12px 12px 12px;
  }
}

/* 对话框内容区域 */
.dialog-content {
  padding: 20px 0;
}

.form-tip {
  font-size: 12px;
  color: var(--el-text-color-placeholder);
  margin-top: 4px;
  line-height: 1.4;
}

.dialog-footer {
  text-align: right;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .dialog-content {
    padding: 16px 0;
  }

  .dialog-footer {
    padding-top: 16px;
    gap: 8px;
  }

  .dialog-footer .el-button {
    flex: 1;
    min-width: 80px;
  }
}

/* 超小屏幕适配 */
@media (max-width: 480px) {
  .dialog-content {
    padding: 12px 0;
  }

  .dialog-footer {
    flex-direction: column-reverse;
    gap: 12px;
  }

  .dialog-footer .el-button {
    width: 100%;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .management-page {
    padding: 16px;
  }

  .search-form :deep(.el-form) {
    flex-direction: column;
  }

  .search-form :deep(.el-form-item) {
    margin-right: 0;
    margin-bottom: 16px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 12px;

    .el-button {
      width: 100%;
      margin-left: 0;
    }
  }
}
</style>
