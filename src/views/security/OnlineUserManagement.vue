<template>
  <div class="management-page">
    <!-- 标签页 -->
    <el-tabs v-model="activeTab" class="main-tabs">
      <!-- 用户列表标签页 -->
      <el-tab-pane label="用户列表" name="userList">
        <div class="tab-content">

          <!-- 搜索和操作区域 -->
          <div class="search-section">
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="关键词">
          <el-input
            v-model="searchForm.keyword"
            placeholder="请输入用户名"
            clearable
            class="search-input"
            @keyup.enter="handleSearch"
          />
        </el-form-item>

        <el-form-item label="设备类型">
          <el-select
            v-model="searchForm.deviceType"
            placeholder="请选择设备类型"
            clearable
            class="search-select"
          >
            <el-option
              v-for="item in deviceTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="排序">
          <el-select
            v-model="searchForm.sortType"
            placeholder="请选择排序方式"
            class="search-select"
          >
            <el-option label="登录时间降序" :value="false" />
            <el-option label="登录时间升序" :value="true" />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>

      <div class="action-buttons">
        <el-button
          type="warning"
          :disabled="!hasSelection"
          @click="handleBatchKickout"
        >
          <el-icon><Close /></el-icon>
          批量踢出
        </el-button>
        <el-button
          type="danger"
          :disabled="!hasSelection"
          @click="handleBatchLogout"
        >
          <el-icon><SwitchButton /></el-icon>
          批量注销
        </el-button>

      </div>
    </div>

          <!-- 数据表格 -->
          <div class="table-section">
      <el-table
        v-loading="loading"
        :data="tableData"
        @selection-change="handleSelectionChange"
        style="width: 100%"
        :header-cell-style="{ background: 'var(--bg-color-table-header)', color: 'var(--text-color-primary)', fontWeight: '600' }"
        :row-style="{ height: '56px' }"
        row-key="loginId"
      >
        <el-table-column type="selection" width="55" />

        <el-table-column prop="username" label="用户名" width="200" align="center" header-align="center" />

        <el-table-column prop="primaryDeviceType" label="主要设备" width="100" align="center" header-align="center">
          <template #default="{ row }">
            <el-tag :type="getDeviceTypeTagType(row.primaryDeviceType)" size="small">
              {{ getDeviceTypeText(row.primaryDeviceType) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="deviceCount" label="设备数" width="80" align="center" header-align="center">
          <template #default="{ row }">
            <el-tag
              :type="getDeviceCountTagType(row.deviceCount)"
              size="small"
              round
              class="device-count-tag"
            >
              {{ row.deviceCount }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="primaryClientIp" label="IP地址" width="180" align="center" header-align="center" />

        <el-table-column prop="location" label="位置" width="180" align="center" header-align="center" show-overflow-tooltip />

        <el-table-column prop="loginTime" label="登录时间" width="160" align="center" header-align="center" />

        <el-table-column prop="onlineDuration" label="在线时长" width="100" align="center" header-align="center">
          <template #default="{ row }">
            {{ formatDuration(row.onlineDuration) }}
          </template>
        </el-table-column>

        <el-table-column prop="onlineStatus" label="状态" width="80" align="center" header-align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.onlineStatus)" size="small">
              {{ getStatusText(row.onlineStatus) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="280" fixed="right" align="center" header-align="center">
          <template #default="{ row }">
            <div class="action-buttons-row">
              <el-tooltip content="查看终端" placement="top">
                <el-button
                  type="info"
                  size="small"
                  @click="handleViewTerminals(row)"
                  :icon="Monitor"
                  circle
                />
              </el-tooltip>
              <el-tooltip content="踢出用户" placement="top">
                <el-button
                  type="warning"
                  size="small"
                  @click="handleKickout(row)"
                  :icon="Close"
                  circle
                />
              </el-tooltip>
              <el-tooltip content="强制注销" placement="top">
                <el-button
                  type="danger"
                  size="small"
                  @click="handleLogout(row)"
                  :icon="SwitchButton"
                  circle
                />
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <Pagination
        v-model:current="pagination.current"
        v-model:pageSize="pagination.size"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        />
          </div>
        </div>
      </el-tab-pane>

      <!-- 统计信息标签页 -->
      <el-tab-pane label="统计信息" name="statistics">
        <div class="tab-content">
          <div class="statistics-content" v-if="statistics">
            <!-- 核心统计 -->
            <div class="core-stats">
              <el-row :gutter="24">
                <el-col :xs="24" :sm="12" :md="12" :lg="8">
                  <div class="main-stat-card">
                    <div class="main-stat-icon">
                      <el-icon><User /></el-icon>
                    </div>
                    <div class="main-stat-content">
                      <div class="main-stat-value">{{ statistics.totalOnlineUsers }}</div>
                      <div class="main-stat-title">总在线用户</div>
                      <div class="main-stat-desc">当前系统在线用户总数</div>
                    </div>
                  </div>
                </el-col>
                <el-col :xs="24" :sm="12" :md="12" :lg="8">
                  <div class="main-stat-card">
                    <div class="main-stat-icon multi-device">
                      <el-icon><Monitor /></el-icon>
                    </div>
                    <div class="main-stat-content">
                      <div class="main-stat-value">{{ statistics.multiDeviceUsers || 0 }}</div>
                      <div class="main-stat-title">多设备用户</div>
                      <div class="main-stat-desc">同时使用多个设备的用户</div>
                    </div>
                  </div>
                </el-col>
                <el-col :xs="24" :sm="24" :md="24" :lg="8">
                  <div class="main-stat-card" v-if="statistics.averageOnlineDuration">
                    <div class="main-stat-icon duration">
                      <el-icon><Clock /></el-icon>
                    </div>
                    <div class="main-stat-content">
                      <div class="main-stat-value">{{ formatDuration(statistics.averageOnlineDuration) }}</div>
                      <div class="main-stat-title">平均在线时长</div>
                      <div class="main-stat-desc">用户平均在线时间</div>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>

            <!-- 设备类型统计 -->
            <div class="device-stats">
              <h3 class="section-title">设备类型分布</h3>
              <el-row :gutter="16">
                <el-col
                  v-for="(count, deviceType) in statistics.deviceTypeStatistics"
                  :key="deviceType"
                  :xs="12" :sm="8" :md="6" :lg="4" :xl="3"
                >
                  <div class="device-stat-card">
                    <div class="device-icon" :class="`device-${deviceType.toLowerCase()}`">
                      <el-icon><Monitor /></el-icon>
                    </div>
                    <div class="device-info">
                      <div class="device-count">{{ count }}</div>
                      <div class="device-name">{{ getDeviceTypeLabel(deviceType) }}</div>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>


          </div>

          <div v-else class="loading-stats">
            <el-skeleton :rows="5" animated />
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 用户终端详情对话框 -->
    <UserTerminalDialog
      v-model:visible="terminalDialogVisible"
      :user-info="selectedUser"
      @refresh="fetchData"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Close,
  SwitchButton,
  Monitor,
  User,
  Clock
} from '@element-plus/icons-vue'
import {
  getOnlineUserList,
  kickoutUser,
  logoutUser,
  batchKickoutUsers,
  getOnlineUserStatistics,
  onlineStatusOptions,
  OperationTypeEnum
} from '@/api/onlineUser'
import Pagination from '@/components/Pagination.vue'
import UserTerminalDialog from './components/UserTerminalDialog.vue'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const selectedItems = ref([])
const statistics = ref(null)
const isMobile = ref(false)
const terminalDialogVisible = ref(false)
const selectedUser = ref({})
const activeTab = ref('userList')

// 搜索表单
const searchForm = reactive({
  keyword: '',
  deviceType: null,
  sortType: false
})

// 分页信息
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 计算属性
const hasSelection = computed(() => {
  return selectedItems.value.length > 0
})

// 动态设备类型选项（从统计信息中获取）
const deviceTypeOptions = computed(() => {
  if (!statistics.value?.deviceTypeStatistics) {
    return []
  }

  return Object.keys(statistics.value.deviceTypeStatistics).map(deviceType => ({
    label: getDeviceTypeLabel(deviceType),
    value: deviceType
  }))
})

// 检查是否为移动端
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

// 获取设备类型文本（动态）
const getDeviceTypeText = (type) => {
  return getDeviceTypeLabel(type)
}

// 获取设备类型标签（动态，支持任意设备类型）
const getDeviceTypeLabel = (type) => {
  // 常见设备类型的中文映射
  const commonTypeMap = {
    'PC': '桌面端',
    'WEB': '网页端',
    'APP': '移动应用',
    'MOBILE': '移动端',
    'HD': '高清设备',
    'TABLET': '平板设备'
  }

  // 如果是常见类型，返回中文名称，否则返回原始类型名
  return commonTypeMap[type] || type
}

// 获取设备类型标签类型（动态，支持任意设备类型）
const getDeviceTypeTagType = (type) => {
  // 常见设备类型的颜色映射
  const commonTypeMap = {
    'PC': 'primary',
    'WEB': 'success',
    'APP': 'warning',
    'MOBILE': 'info',
    'HD': 'danger',
    'TABLET': 'warning'
  }

  // 如果是常见类型，返回对应颜色，否则使用默认颜色
  return commonTypeMap[type] || 'info'
}

// 格式化时长
const formatDuration = (seconds) => {
  if (!seconds || seconds < 0) return '0分钟'

  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)

  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else {
    return `${minutes}分钟`
  }
}

// 获取设备数量标签类型
const getDeviceCountTagType = (count) => {
  if (count === 1) return 'info'
  if (count === 2) return 'success'
  if (count >= 3 && count <= 4) return 'warning'
  if (count >= 5) return 'danger'
  return 'info'
}



// 获取状态文本
const getStatusText = (status) => {
  const option = onlineStatusOptions.find(item => item.value === status)
  return option ? option.label : status
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  const statusMap = {
    'ONLINE': 'success',
    'IDLE': 'warning',
    'OFFLINE': 'danger'
  }
  return statusMap[status] || 'info'
}



// 获取统计信息
const fetchStatistics = async () => {
  try {
    const response = await getOnlineUserStatistics()
    statistics.value = response
  } catch (error) {
    console.error('获取统计信息失败:', error)
  }
}

// 查询数据
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      current: pagination.current,
      size: pagination.size,
      ...searchForm
    }
    const response = await getOnlineUserList(params)
    tableData.value = response.records || []
    pagination.total = response.total || 0

    // 同时更新统计信息
    if (response.statistics) {
      statistics.value = response.statistics
    }
  } catch (error) {
    console.error('获取在线用户列表失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 处理搜索
const handleSearch = () => {
  pagination.current = 1
  fetchData()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    deviceType: null,
    sortType: false
  })
  pagination.current = 1
  fetchData()
}

// 处理选择变化
const handleSelectionChange = (selection) => {
  selectedItems.value = selection
}

// 分页处理
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1
  fetchData()
}

const handleCurrentChange = (current) => {
  pagination.current = current
  fetchData()
}



// 查看用户终端信息
const handleViewTerminals = (row) => {
  selectedUser.value = {
    ...row,
    terminals: row.terminals || []
  }
  terminalDialogVisible.value = true
}





// 踢出用户
const handleKickout = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要踢出用户"${row.username}"吗？用户将被标记为"已被踢下线"状态。`,
      '踢出用户确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await kickoutUser(row.loginId)
    ElMessage.success('踢出用户成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('踢出用户失败:', error)
      ElMessage.error('踢出用户失败')
    }
  }
}

// 强制注销用户
const handleLogout = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要强制注销用户"${row.username}"吗？这将完全清除用户的Token信息。`,
      '强制注销确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await logoutUser(row.loginId)
    ElMessage.success('强制注销成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('强制注销失败:', error)
      ElMessage.error('强制注销失败')
    }
  }
}

// 批量踢出用户
const handleBatchKickout = async () => {
  const loginIds = selectedItems.value.map(item => item.loginId)

  try {
    await ElMessageBox.confirm(
      `确定要踢出选中的${loginIds.length}个用户吗？`,
      '批量踢出确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await batchKickoutUsers({
      loginIds,
      operationType: OperationTypeEnum.KICKOUT,
      reason: '管理员批量踢出'
    })

    ElMessage.success('批量踢出成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量踢出失败:', error)
      ElMessage.error('批量踢出失败')
    }
  }
}

// 批量强制注销用户
const handleBatchLogout = async () => {
  const loginIds = selectedItems.value.map(item => item.loginId)

  try {
    await ElMessageBox.confirm(
      `确定要强制注销选中的${loginIds.length}个用户吗？这将完全清除这些用户的Token信息。`,
      '批量强制注销确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await batchKickoutUsers({
      loginIds,
      operationType: OperationTypeEnum.LOGOUT,
      reason: '管理员批量强制注销'
    })

    ElMessage.success('批量强制注销成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量强制注销失败:', error)
      ElMessage.error('批量强制注销失败')
    }
  }
}

// 生命周期
onMounted(async () => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
  // 先获取统计信息，确保设备类型选项能正确生成
  await fetchStatistics()
  // 然后获取用户数据
  fetchData()
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<style scoped>
/* 使用全局统一的管理页面样式 */

/* 统计信息卡片样式 */
.statistics-section {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-content {
  padding: 16px;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: var(--el-color-primary);
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: var(--el-text-color-regular);
}

/* 搜索表单控件样式 */
.search-input {
  width: 200px;
}

.search-select {
  width: 140px;
}

/* 表格操作按钮 */
.action-buttons-row {
  display: flex;
  gap: 4px;
  align-items: center;
  justify-content: center;
}

/* 设备数量标签 */
.device-count-tag {
  font-weight: 600;
  min-width: 24px;
  text-align: center;
}

/* 标签页样式 */
.main-tabs {
  margin-bottom: 0;
}

.main-tabs .el-tabs__header {
  margin-bottom: 20px;
  border-bottom: 2px solid var(--el-border-color-lighter);
}

.main-tabs .el-tabs__nav-wrap::after {
  display: none;
}

.main-tabs .el-tabs__item {
  font-size: 16px;
  font-weight: 500;
  padding: 0 24px;
  height: 48px;
  line-height: 48px;
  color: var(--el-text-color-regular);
}

.main-tabs .el-tabs__item.is-active {
  color: var(--el-color-primary);
  font-weight: 600;
}

/* 暗黑模式适配 */
.dark .main-tabs .el-tabs__header {
  border-bottom-color: var(--el-border-color);
}

.dark .main-tabs .el-tabs__item {
  color: var(--el-text-color-regular);
}

.dark .main-tabs .el-tabs__item:hover {
  color: var(--el-text-color-primary);
}

.tab-content {
  min-height: 400px;
}

/* 统计信息样式 */
.statistics-content {
  padding: 0;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 20px 0;
  padding-bottom: 10px;
  border-bottom: 2px solid var(--el-color-primary);
  display: inline-block;
}

/* 暗黑模式下section标题适配 */
.dark .section-title {
  color: var(--el-text-color-primary);
  border-bottom-color: var(--el-color-primary-light-3);
}

/* 核心统计样式 */
.core-stats {
  margin-bottom: 40px;
}

.main-stat-card {
  background: var(--el-bg-color);
  border-radius: 16px;
  padding: 24px;
  box-shadow: var(--el-box-shadow-light);
  transition: all 0.3s ease;
  border: 1px solid var(--el-border-color-light);
  height: 140px;
  display: flex;
  align-items: center;
}

.main-stat-card:hover {
  box-shadow: var(--el-box-shadow);
  border-color: var(--el-color-primary-light-7);
}

/* 暗黑模式适配 */
.dark .main-stat-card {
  background: var(--el-bg-color-page);
  border-color: var(--el-border-color);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.dark .main-stat-card:hover {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  border-color: var(--el-color-primary-light-3);
}



.main-stat-icon {
  width: 80px;
  height: 80px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 32px;
  color: white;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  flex-shrink: 0;
}

.main-stat-icon.multi-device {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.main-stat-icon.duration {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
}

.main-stat-content {
  flex: 1;
  min-width: 0;
}

.main-stat-value {
  font-size: 36px;
  font-weight: 700;
  color: var(--el-text-color-primary);
  line-height: 1;
  margin-bottom: 8px;
}

.main-stat-title {
  font-size: 16px;
  color: var(--el-text-color-primary);
  font-weight: 600;
  margin-bottom: 4px;
}

.main-stat-desc {
  font-size: 12px;
  color: var(--el-text-color-regular);
  line-height: 1.4;
}

/* 设备统计样式 */
.device-stats {
  margin-bottom: 40px;
}

.device-stat-card {
  display: flex;
  align-items: center;
  padding: 16px;
  background: var(--el-bg-color);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-lighter);
  transition: all 0.3s ease;
  height: 80px;
}

.device-stat-card:hover {
  border-color: var(--el-color-primary);
  box-shadow: var(--el-box-shadow-light);
}

/* 暗黑模式适配 */
.dark .device-stat-card {
  background: var(--el-bg-color-page);
  border-color: var(--el-border-color);
}

.dark .device-stat-card:hover {
  border-color: var(--el-color-primary-light-3);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.device-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 20px;
  color: white;
  background: var(--el-color-primary);
}

.device-icon.device-pc {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.device-icon.device-web {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.device-icon.device-mobile {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.device-icon.device-app {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.device-info {
  flex: 1;
}

.device-count {
  font-size: 24px;
  font-weight: bold;
  color: var(--el-text-color-primary);
  line-height: 1;
  margin-bottom: 2px;
}

.device-name {
  font-size: 12px;
  color: var(--el-text-color-regular);
}



.loading-stats {
  padding: 40px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .main-tabs .el-tabs__item {
    font-size: 14px;
    padding: 0 16px;
    height: 44px;
    line-height: 44px;
  }

  .tab-content {
    min-height: 300px;
  }

  .main-stat-card {
    padding: 20px;
    height: 120px;
  }

  .main-stat-icon {
    width: 60px;
    height: 60px;
    font-size: 24px;
    margin-right: 16px;
  }

  .main-stat-value {
    font-size: 28px;
  }

  .main-stat-title {
    font-size: 14px;
  }

  .main-stat-desc {
    font-size: 11px;
  }

  .device-stat-card {
    padding: 12px;
    height: 70px;
  }

  .device-icon {
    width: 40px;
    height: 40px;
    font-size: 16px;
    margin-right: 10px;
  }

  .device-count {
    font-size: 20px;
  }

  .device-name {
    font-size: 11px;
  }



  .section-title {
    font-size: 16px;
  }
}
</style>
