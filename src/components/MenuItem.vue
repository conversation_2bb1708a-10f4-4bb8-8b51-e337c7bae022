<template>
  <!-- 单级菜单 -->
  <el-menu-item
    v-if="!menuItem.children || menuItem.children.length === 0"
    :index="menuItem.path"
    class="menu-item"
  >
    <template v-if="menuItem.icon">
      <svg-icon :name="menuItem.icon" color="var(--menu-icon-color)" class="menu-icon" />
    </template>
    <template #title>
      <span class="menu-title">{{ menuItem.title }}</span>
    </template>
  </el-menu-item>

  <!-- 多级菜单 -->
  <el-sub-menu
    v-else
    :index="menuItem.path"
    class="sub-menu"
  >
    <template #title>
      <template v-if="menuItem.icon">
        <svg-icon :name="menuItem.icon" color="var(--menu-item-color)" class="menu-icon" />
      </template>
      <span class="menu-title">{{ menuItem.title }}</span>
    </template>
    
    <!-- 递归渲染子菜单 -->
    <MenuItemRecursive 
      v-for="child in menuItem.children" 
      :key="child.path"
      :menu-item="child"
      :level="level + 1"
    />
  </el-sub-menu>
</template>

<script>
import { Document } from '@element-plus/icons-vue'

export default {
  name: 'MenuItemRecursive',
  components: {
    Document
  },
  props: {
    menuItem: {
      type: Object,
      required: true
    },
    level: {
      type: Number,
      default: 1
    }
  }
}
</script>

<style lang="scss" scoped>


.menu-icon {
  margin-right: 8px;
  font-size: 18px;
  color: var(--menu-item-color); // 使用菜单项专用颜色
  transition: all 0.3s ease;

  // 确保Element Plus图标也使用相同颜色
  &.el-icon {
    color: var(--menu-item-color);
  }
}

.menu-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--menu-item-color); // 使用菜单项专用颜色，与图标保持一致
  transition: all 0.3s ease;
}

// 子菜单折叠图标样式
:deep(.el-sub-menu__icon-arrow) {
  color: var(--menu-item-color) !important; // 与标题使用相同颜色变量
  transition: all 0.3s ease;
}

// 确保子菜单标题区域的所有图标都使用统一颜色
:deep(.el-sub-menu__title) {
  color: var(--menu-item-color) !important;

  .el-icon {
    color: var(--menu-item-color) !important;
  }
}

</style>
