/**
 * Token状态检查器
 * 用于检测用户是否被踢出或token是否失效
 */
import { ElMessage, ElMessageBox } from 'element-plus'
import { handle401Error } from './clearUserData'
import { useAuthStore } from '@/stores/auth'

class TokenStatusChecker {
  constructor() {
    this.timer = null
    this.isChecking = false
    this.checkInterval = 30000 // 30秒检查一次
    this.retryCount = 0
    this.maxRetries = 3
    this.isEnabled = false
  }

  /**
   * 启动定时检查
   */
  start() {
    if (this.timer) {
      this.stop()
    }

    this.isEnabled = true
    this.timer = setInterval(() => {
      this.checkTokenStatus()
    }, this.checkInterval)

    console.log('Token状态检查器已启动，检查间隔:', this.checkInterval / 1000, '秒')
  }

  /**
   * 停止定时检查
   */
  stop() {
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }
    this.isEnabled = false
    this.retryCount = 0
    console.log('Token状态检查器已停止')
  }

  /**
   * 检查Token状态
   */
  async checkTokenStatus() {
    if (this.isChecking || !this.isEnabled) {
      return
    }

    const authStore = useAuthStore()
    
    // 如果用户未登录，停止检查
    if (!authStore.isLoggedIn()) {
      this.stop()
      return
    }

    try {
      this.isChecking = true
      
      // 发送一个轻量级的API请求来检查token状态
      // 使用获取用户信息接口，因为它需要认证且响应较小
      const response = await fetch('/api/auth/userinfo', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      })

      // 如果返回401，说明token已失效或用户被踢出
      if (response.status === 401) {
        this.handleTokenInvalid('您的登录状态已失效，可能是被管理员踢出或登录过期')
        return
      }

      // 如果返回403，说明权限不足（可能是被禁用）
      if (response.status === 403) {
        this.handleTokenInvalid('您的账号权限已被限制，请联系管理员')
        return
      }

      // 检查响应数据
      if (response.ok) {
        const data = await response.json()
        
        // 如果后端返回特定的踢出状态码
        if (data.code === -1001) { // 假设-1001表示用户被踢出
          this.handleTokenInvalid('您已被管理员踢出，请重新登录')
          return
        }
      }

      // 重置重试计数
      this.retryCount = 0
      
    } catch (error) {
      console.warn('检查Token状态失败:', error)
      
      // 网络错误或其他错误，增加重试计数
      this.retryCount++
      
      // 如果连续失败次数过多，停止检查
      if (this.retryCount >= this.maxRetries) {
        console.error('Token状态检查连续失败，停止检查')
        this.stop()
      }
    } finally {
      this.isChecking = false
    }
  }

  /**
   * 处理Token失效
   */
  async handleTokenInvalid(message = '登录状态异常，请重新登录') {
    this.stop() // 停止检查

    try {
      await ElMessageBox.alert(message, '登录状态异常', {
        confirmButtonText: '重新登录',
        type: 'warning',
        showClose: false,
        closeOnClickModal: false,
        closeOnPressEscape: false
      })
    } catch (error) {
      // 用户可能关闭了对话框，直接处理
    }

    // 清理用户数据并跳转到登录页
    handle401Error()
  }

  /**
   * 设置检查间隔
   */
  setInterval(interval) {
    this.checkInterval = interval
    if (this.timer) {
      this.stop()
      this.start()
    }
  }

  /**
   * 立即执行一次检查
   */
  checkNow() {
    if (!this.isEnabled) {
      return
    }
    this.checkTokenStatus()
  }

  /**
   * 获取当前状态
   */
  getStatus() {
    return {
      isEnabled: this.isEnabled,
      isChecking: this.isChecking,
      checkInterval: this.checkInterval,
      retryCount: this.retryCount
    }
  }
}

// 创建全局实例
export const tokenStatusChecker = new TokenStatusChecker()

// 导出便捷方法
export const startTokenChecker = () => tokenStatusChecker.start()
export const stopTokenChecker = () => tokenStatusChecker.stop()
export const checkTokenNow = () => tokenStatusChecker.checkNow()

export default tokenStatusChecker
