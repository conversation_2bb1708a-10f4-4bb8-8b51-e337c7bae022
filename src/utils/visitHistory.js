/**
 * 访问历史管理工具
 */

const VISIT_HISTORY_KEY = 'user_visit_history'
const MAX_HISTORY_COUNT = 10

/**
 * 获取访问历史记录
 * @returns {Array} 访问历史记录数组
 */
export function getVisitHistory() {
  try {
    const history = localStorage.getItem(VISIT_HISTORY_KEY)
    return history ? JSON.parse(history) : []
  } catch (error) {
    console.error('获取访问历史失败:', error)
    return []
  }
}

/**
 * 添加访问记录
 * @param {Object} visitRecord 访问记录
 * @param {string} visitRecord.path 路由路径
 * @param {string} visitRecord.name 菜单名称
 * @param {string} visitRecord.icon 菜单图标
 * @param {string} visitRecord.title 页面标题
 */
export function addVisitRecord(visitRecord) {
  try {
    const history = getVisitHistory()

    // 检查是否已存在相同路径的记录
    const existingIndex = history.findIndex(item => item.path === visitRecord.path)

    // 创建新的访问记录
    const newRecord = {
      ...visitRecord,
      visitTime: new Date().toISOString(),
      id: Date.now() + Math.random()
    }

    if (existingIndex !== -1) {
      // 如果已存在，移除旧记录
      history.splice(existingIndex, 1)
    }

    // 将新记录添加到开头
    history.unshift(newRecord)

    // 限制记录数量
    if (history.length > MAX_HISTORY_COUNT) {
      history.splice(MAX_HISTORY_COUNT)
    }

    // 保存到localStorage
    localStorage.setItem(VISIT_HISTORY_KEY, JSON.stringify(history))
  } catch (error) {
    console.error('添加访问记录失败:', error)
  }
}

/**
 * 清空访问历史
 */
export function clearVisitHistory() {
  try {
    localStorage.removeItem(VISIT_HISTORY_KEY)
  } catch (error) {
    console.error('清空访问历史失败:', error)
  }
}

/**
 * 格式化访问时间
 * @param {string} time ISO时间字符串
 * @returns {string} 格式化后的时间
 */
export function formatVisitTime(time) {
  if (!time) return ''
  
  const date = new Date(time)
  const now = new Date()
  const diff = now - date
  
  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  if (diff < 604800000) return `${Math.floor(diff / 86400000)}天前`
  
  return date.toLocaleDateString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}
