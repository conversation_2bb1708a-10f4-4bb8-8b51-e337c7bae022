# 在线用户管理接口文档

## 概述

在线用户管理功能基于Sa-Token框架实现，提供用户会话查询、管理、踢出等核心功能。支持多设备登录管理和批量操作，所有敏感操作都需要相应的权限验证。

**基础路径**: `/online-user`

**权限要求**:
- 查看权限: `online-user:view` 或管理员角色
- 管理权限: `online-user:manage` 或管理员角色

### 🔧 **8个核心接口**
1. **GET** `/online-user/list` - 获取在线用户列表（分页查询，包含终端信息）
2. **POST** `/online-user/logout` - 强制注销用户
3. **POST** `/online-user/kickout` - 踢出用户
4. **POST** `/online-user/batch-kickout` - 批量踢出用户
5. **DELETE** `/online-user/token-md5/{tokenMd5}` - 根据Token MD5踢出用户
6. **GET** `/online-user/statistics` - 获取统计信息
7. **GET** `/online-user/{loginId}/online-status` - 检查在线状态
8. **GET** `/online-user/{loginId}/device-count` - 获取设备数量

---

## 1. 获取在线用户列表

**接口地址**: `GET /online-user/list`

**功能描述**: 分页查询当前系统中的在线用户，支持关键词搜索、设备类型筛选等功能

**权限要求**: `online-user:view`

### 请求参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| current | Long | 否 | 1 | 当前页码，从1开始 |
| size | Long | 否 | 10 | 每页大小，建议不超过100 |
| keyword | String | 否 | - | 搜索关键词，支持用户ID或用户名模糊搜索 |
| deviceType | String | 否 | - | 设备类型筛选（PC、WEB、APP、MOBILE等） |
| sortType | Boolean | 否 | false | 排序类型，true为升序，false为降序 |

### 请求示例

```http
GET /online-user/list?current=1&size=10&keyword=admin&deviceType=PC&sortType=false
```

### 响应参数

```json
{
  "code": 200,
  "message": "查询在线用户列表成功",
  "data": {
    "current": 1,
    "size": 10,
    "total": 25,
    "pages": 3,
    "records": [
      {
        "loginId": "1001",
        "username": "admin",
        "userTypeId": "1",
        "sessionId": "satoken:login:session:1001",
        "loginTime": "2025-08-02 10:30:00",
        "deviceCount": 2,
        "primaryDeviceType": "PC",
        "primaryClientIp": "192.168.*.*",
        "sessionTimeout": 7200,
        "terminals": [
          {
            "index": 1,
            "deviceType": "PC",
            "tokenValue": "abc123***xyz9",
            "tokenMd5": "5d41402abc4b2a76b9719d911017c592",
            "loginTime": "2025-08-02 10:30:00",
            "deviceId": "PC_192.168.1.100",
            "clientIp": "192.168.*.*",
            "location": "北京-北京",
            "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64)",
            "isCurrent": true,
            "tokenTimeout": 7200,
            "onlineDuration": 14100,
            "extraInfo": "{\"location\":\"北京\"}"
          },
          {
            "index": 2,
            "deviceType": "MOBILE",
            "tokenValue": "def456***abc1",
            "tokenMd5": "098f6bcd4621d373cade4e832627b4f6",
            "loginTime": "2025-08-02 11:15:00",
            "deviceId": "MOBILE_192.168.1.101",
            "clientIp": "192.168.*.*",
            "location": "北京-北京",
            "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0)",
            "isCurrent": false,
            "tokenTimeout": 7200,
            "onlineDuration": 10500,
            "extraInfo": null
          }
        ],
        "onlineStatus": "ONLINE",
        "location": "北京-北京",
        "onlineDuration": 14100
      }
    ],
    "hasPrevious": false,
    "hasNext": true,
    "statistics": {
      "totalOnlineUsers": 25,
      "deviceTypeStatistics": {
        "PC": 10,
        "WEB": 8,
        "MOBILE": 3,
        "APP": 2,
        "HD": 1,
        "TABLET": 1
      },
      "adminUsers": 3,
      "normalUsers": 22,
      "multiDeviceUsers": 7,
      "averageOnlineDuration": 3600
    }
  }
}
```

### 响应字段说明

#### OnlineUserVO 字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| loginId | String | 用户登录ID |
| username | String | 用户名 |
| userTypeId | String | 用户类型ID |
| sessionId | String | Sa-Token会话ID |
| loginTime | String | 最近登录时间 |
| deviceCount | Integer | 登录设备数量 |
| primaryDeviceType | String | 主要设备类型 |
| primaryClientIp | String | 主要登录IP（脱敏） |
| sessionTimeout | Long | 会话剩余有效期（秒） |
| terminals | Array | 终端信息列表 |
| onlineStatus | String | 在线状态（ONLINE/IDLE/OFFLINE） |
| location | String | 地理位置信息 |
| onlineDuration | Long | 在线时长（秒） |

#### UserTerminalVO 字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| index | Integer | 终端索引值 |
| deviceType | String | 设备类型 |
| tokenValue | String | Token值（脱敏显示） |
| tokenMd5 | String | Token的MD5哈希值（用于踢出操作） |
| loginTime | String | 登录时间 |
| deviceId | String | 设备ID |
| clientIp | String | 客户端IP（脱敏） |
| location | String | 地理位置信息 |
| userAgent | String | 用户代理信息 |
| isCurrent | Boolean | 是否为当前会话 |
| tokenTimeout | Long | Token剩余有效期（秒） |
| onlineDuration | Long | 在线时长（秒） |
| extraInfo | String | 扩展信息 |

---

## 2. 强制注销用户

**接口地址**: `POST /online-user/logout`

**功能描述**: 强制指定用户下线，完全清除Token信息

**权限要求**: `online-user:manage`

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| loginId | String | 是 | 用户登录ID |
| deviceType | String | 否 | 设备类型，为空时注销所有设备 |

### 请求示例

```http
POST /online-user/logout?loginId=1001&deviceType=PC
```

### 响应参数

```json
{
  "code": 200,
  "message": "强制注销用户成功 (设备类型: PC)",
  "data": null
}
```

---

## 3. 踢出用户

**接口地址**: `POST /online-user/kickout`

**功能描述**: 将指定用户踢下线，Token被标记为"已被踢下线"状态

**权限要求**: `online-user:manage`

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| loginId | String | 是 | 用户登录ID |
| deviceType | String | 否 | 设备类型，为空时踢出所有设备 |

### 请求示例

```http
POST /online-user/kickout?loginId=1001&deviceType=MOBILE
```

### 响应参数

```json
{
  "code": 200,
  "message": "踢出用户成功 (设备类型: MOBILE)",
  "data": null
}
```

---

## 4. 批量踢出用户

**接口地址**: `POST /online-user/batch-kickout`

**功能描述**: 根据用户ID列表批量执行踢出操作，支持强制注销和踢人下线两种操作类型

**权限要求**: `online-user:manage` + Google验证码

### 请求参数

```json
{
  "loginIds": ["1001", "1002", "1003"],
  "deviceType": "PC",
  "operationType": "KICKOUT",
  "reason": "系统维护"
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| loginIds | List<String> | 是 | 用户ID列表，最多100个 |
| deviceType | String | 否 | 设备类型，为空时操作所有设备 |
| operationType | String | 否 | 操作类型：LOGOUT（强制注销）、KICKOUT（踢出） |
| reason | String | 否 | 操作原因，用于记录日志 |

### 响应参数

```json
{
  "code": 200,
  "message": "批量踢出用户完成，成功踢出 3 个用户",
  "data": 3
}
```

---

## 5. 根据Token MD5踢出用户

**接口地址**: `DELETE /online-user/token-md5/{tokenMd5}`

**功能描述**: 根据Token的MD5哈希值匹配并踢出对应的用户会话，安全且精确

**权限要求**: `online-user:manage`

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| tokenMd5 | String | 是 | Token的MD5哈希值（路径参数） |

### 请求示例

```http
DELETE /online-user/token-md5/5d41402abc4b2a76b9719d911017c592
```

### 响应参数

```json
{
  "code": 200,
  "message": "踢出用户成功",
  "data": null
}
```

---

## 6. 获取在线用户统计信息

**接口地址**: `GET /online-user/statistics`

**功能描述**: 统计当前系统中在线用户的各项数据

**权限要求**: `online-user:view`

### 响应参数

```json
{
  "code": 200,
  "message": "获取在线用户统计信息成功",
  "data": {
    "totalOnlineUsers": 25,
    "deviceTypeStatistics": {
      "PC": 10,
      "WEB": 8,
      "MOBILE": 3,
      "APP": 2,
      "HD": 1,
      "TABLET": 1
    },
    "adminUsers": 3,
    "normalUsers": 22,
    "multiDeviceUsers": 7,
    "averageOnlineDuration": 3600
  }
}
```

---

## 7. 检查用户在线状态

**接口地址**: `GET /online-user/{loginId}/online-status`

**功能描述**: 检查指定用户是否处于在线状态

**权限要求**: `online-user:view`

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| loginId | String | 是 | 用户登录ID（路径参数） |

### 请求示例

```http
GET /online-user/1001/online-status
```

### 响应参数

```json
{
  "code": 200,
  "message": "检查用户在线状态成功",
  "data": true
}
```

---

## 8. 获取用户在线设备数量

**接口地址**: `GET /online-user/{loginId}/device-count`

**功能描述**: 统计指定用户当前在线的设备数量

**权限要求**: `online-user:view`

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| loginId | String | 是 | 用户登录ID（路径参数） |

### 请求示例

```http
GET /online-user/1001/device-count
```

### 响应参数

```json
{
  "code": 200,
  "message": "获取用户设备数量成功",
  "data": 2
}
```

---

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 400 | 请求参数错误 |
| 401 | 未登录或登录已过期 |
| 403 | 权限不足 |
| 500 | 服务器内部错误 |

## 数据字典

### 设备类型 (deviceType)

设备类型是动态的，由Sa-Token框架和客户端登录时设置。常见的设备类型包括但不限于：

| 值 | 说明 |
|----|------|
| PC | 桌面端 |
| WEB | 网页端 |
| APP | 移动应用 |
| MOBILE | 移动端 |
| HD | 高清设备 |
| TABLET | 平板设备 |
| 其他 | 系统中实际存在的任意设备类型 |

**注意**: 设备类型统计是完全动态的，会自动统计系统中实际存在的所有设备类型，不受上述列表限制。

### 统计信息结构 (OnlineUserStatistics)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| totalOnlineUsers | Long | 总在线用户数 |
| deviceTypeStatistics | Map<String, Long> | 按设备类型统计，Key为设备类型，Value为该类型的用户数 |
| adminUsers | Long | 管理员用户数（userTypeId为管理员类型的用户） |
| normalUsers | Long | 普通用户数（总数减去管理员数） |
| multiDeviceUsers | Long | 多设备登录用户数（同时在多个设备登录的用户） |
| averageOnlineDuration | Long | 平均在线时长（单位：秒） |

**deviceTypeStatistics示例**:
```json
{
  "PC": 10,
  "WEB": 8,
  "MOBILE": 3,
  "APP": 2,
  "HD": 1,
  "TABLET": 1,
  "CUSTOM_TYPE": 2
}
```

### 在线状态 (onlineStatus)

| 值 | 说明 |
|----|------|
| ONLINE | 在线 |
| IDLE | 空闲 |
| OFFLINE | 离线 |

### 操作类型 (operationType)

| 值 | 说明 |
|----|------|
| LOGOUT | 强制注销，完全清除Token信息 |
| KICKOUT | 踢人下线，标记为"已被踢下线"状态 |

---
