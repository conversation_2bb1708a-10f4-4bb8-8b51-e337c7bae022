# 在线用户管理功能说明

## 概述

在线用户管理功能已根据最新接口文档完成更新，采用弹窗对话框方式展示终端信息，提供完整的在线用户查看、管理和操作功能。该功能基于Sa-Token框架实现，支持多设备登录管理和批量操作，具备优秀的移动端适配。

## 最新更新内容

### UI交互方式调整
1. **恢复弹窗对话框**：将展开行方式改回为弹窗对话框展示终端信息
2. **移动端优化**：针对移动设备进行了全面的响应式设计优化
3. **操作体验提升**：优化了按钮布局和交互方式，提升用户体验

### 接口适配更新
1. **终端在线时长**：新增终端级别的在线时长显示
2. **数据结构完善**：完全适配最新的接口响应结构
3. **动态设备类型**：支持完全动态的设备类型管理

## 功能特性

### 1. 动态统计信息展示
- 总在线用户数
- 完全动态的设备类型统计（自动适应任意设备类型）
- 管理员用户数、普通用户数、多设备用户数
- 平均在线时长统计

### 2. 智能用户列表管理
- 分页查询在线用户列表
- 支持关键词搜索（用户ID或用户名）
- 动态设备类型筛选（选项根据实际在线设备动态生成）
- 排序功能（按登录时间升序/降序）

### 3. 用户信息展示
- 用户名
- 主要设备类型（动态标签，支持任意类型）
- 在线设备数量（彩色标签显示）
- IP地址和位置信息
- 登录时间和在线时长
- 在线状态（在线/空闲/离线）

### 4. 弹窗终端管理
- 点击"查看终端"按钮打开终端详情对话框
- 终端信息包含：序号、设备类型、IP地址、位置、登录时间、在线时长、是否当前终端
- 按设备类型踢出特定终端
- 根据Token MD5值踢出特定会话
- 支持终端信息刷新

### 5. 用户操作功能
- **查看终端**：弹窗方式查看用户所有登录终端详情
- **踢出用户**：将用户标记为"已被踢下线"状态
- **强制注销**：完全清除用户Token信息
- **批量操作**：支持批量踢出和批量强制注销

## 移动端适配特性

### 1. 响应式对话框
- 移动端对话框宽度自适应（95%）
- 桌面端固定宽度（1100px）
- 对话框高度限制，支持滚动

### 2. 表格优化
- 移动端隐藏位置列，节省空间
- 时间显示格式简化（MM-DD HH:mm）
- 表格字体和间距适配小屏幕
- 操作按钮在移动端显示文字而非图标

### 3. 交互优化
- 移动端按钮尺寸适中，便于触摸操作
- 对话框底部按钮在移动端垂直排列
- 表格行高和字体大小针对移动端优化

### 4. 布局适配
```css
@media (max-width: 768px) {
  .terminal-dialog .el-dialog {
    margin: 2vh auto 20px;
    width: 95% !important;
  }
  
  .dialog-footer {
    flex-direction: column-reverse;
  }
  
  .action-buttons {
    flex-direction: column;
  }
}
```

## 文件结构

```
src/
├── api/
│   └── onlineUser.js                    # API接口（动态设备类型支持）
├── views/
│   └── security/
│       ├── OnlineUserManagement.vue     # 主页面（弹窗方式）
│       └── components/
│           └── UserTerminalDialog.vue   # 终端详情对话框（移动端优化）
└── config/
    └── componentMap.js                  # 组件映射配置
```

## 核心组件说明

### UserTerminalDialog.vue 特性
1. **响应式设计**：自动检测移动端并调整布局
2. **智能列显示**：移动端隐藏非关键列
3. **操作按钮适配**：移动端显示文字，桌面端显示图标
4. **时间格式优化**：移动端简化时间显示格式
5. **触摸友好**：按钮大小和间距适合触摸操作

### 移动端检测逻辑
```javascript
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

const dialogWidth = computed(() => {
  if (isMobile.value) {
    return '95%'
  }
  return '1100px'
})
```

## API接口

### 主要接口
1. `GET /online-user/list` - 获取在线用户列表（包含终端信息和统计信息）
2. `POST /online-user/logout` - 强制注销用户
3. `POST /online-user/kickout` - 踢出用户
4. `POST /online-user/batch-kickout` - 批量踢出用户
5. `DELETE /online-user/token-md5/{tokenMd5}` - 根据Token MD5踢出用户
6. `GET /online-user/statistics` - 获取统计信息
7. `GET /online-user/{loginId}/online-status` - 检查用户在线状态
8. `GET /online-user/{loginId}/device-count` - 获取用户在线设备数量

### 权限要求
- 查看权限：`online-user:view`
- 管理权限：`online-user:manage`

## 数据结构

### 终端信息结构（新增在线时长）
```json
{
  "index": 1,
  "deviceType": "PC",
  "tokenMd5": "5d41402abc4b2a76b9719d911017c592",
  "clientIp": "192.168.*.*",
  "location": "北京-北京",
  "loginTime": "2025-08-02 10:30:00",
  "onlineDuration": 14100,
  "isCurrent": true
}
```

### 用户数据结构
```json
{
  "loginId": "1001",
  "username": "admin",
  "deviceCount": 2,
  "primaryDeviceType": "PC",
  "terminals": [
    // 终端信息数组
  ],
  "onlineStatus": "ONLINE",
  "onlineDuration": 14100
}
```

## 使用方法

### 菜单配置
在后台管理系统中添加菜单项：
- 菜单名称：在线用户管理
- 路由路径：security/online-users
- 组件路径：OnlineUserManagement
- 权限标识：online-user:view

### 操作流程
1. **查看用户列表**：进入页面自动加载在线用户列表
2. **筛选用户**：使用关键词和设备类型进行筛选
3. **查看终端**：点击操作列的"查看终端"按钮
4. **管理终端**：在弹出的对话框中对特定终端进行操作
5. **批量操作**：选择多个用户进行批量踢出或注销

## 优势特性

### 1. 用户体验优化
- 弹窗方式避免页面跳转，操作更流畅
- 移动端完全适配，触摸操作友好
- 智能布局，关键信息优先显示

### 2. 技术特性
- 完全动态的设备类型支持
- 响应式设计，适配各种屏幕尺寸
- 组件化设计，易于维护和扩展

### 3. 功能完整性
- 支持所有终端管理操作
- 实时数据刷新
- 完善的错误处理和用户提示

## 注意事项

1. **移动端体验**：在移动设备上使用时，对话框会自动适配屏幕尺寸
2. **权限控制**：所有操作都需要相应权限验证
3. **数据实时性**：终端信息支持手动刷新获取最新状态
4. **安全考虑**：使用Token MD5进行操作，避免敏感信息泄露
5. **性能优化**：大量用户时建议使用筛选功能减少数据量

---

**文档版本**: v2.0
**更新时间**: 2025-08-02
**适配接口**: 在线用户管理接口文档 v1.4
**UI方式**: 弹窗对话框 + 移动端优化
