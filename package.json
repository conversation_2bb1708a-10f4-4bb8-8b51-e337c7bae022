{"name": "akey-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint src --ext .vue,.js,.jsx,.ts,.tsx --fix", "lint:check": "eslint src --ext .vue,.js,.jsx,.ts,.tsx", "format": "prettier --write \"src/**/*.{vue,js,jsx,ts,tsx,json,css,scss,md}\"", "format:check": "prettier --check \"src/**/*.{vue,js,jsx,ts,tsx,json,css,scss,md}\"", "type-check": "vue-tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.10.0", "echarts": "^6.0.0", "element-plus": "^2.10.3", "jsencrypt": "^3.3.2", "pinia": "^3.0.3", "qrcode": "^1.5.4", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.0", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-vue": "^10.3.0", "prettier": "^3.6.2", "sass": "^1.89.2", "vite": "^7.0.0"}}